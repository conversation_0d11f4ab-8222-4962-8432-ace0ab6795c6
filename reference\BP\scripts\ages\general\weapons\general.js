import { ItemComponentTypes, ItemStack, system, world } from "@minecraft/server";
export function dropArrow(arrow, arrowTypeId) {
    const arrowLocation = arrow.location;
    const player = arrow.dimension.getPlayers({ location: arrowLocation, closest: 1 })[0];
    const playerLocation = player.location;
    arrow.dimension.spawnItem(new ItemStack(arrowTypeId), playerLocation);
    arrow.remove();
}
export function dropWeaponWithNewDurability(thrownItem, weaponId) {
    const itemId = weaponId.replace('thrown_', '');
    const delayDrop = system.runTimeout(() => {
        const durability = thrownItem.getProperty('va:durability');
        dropWeapon(thrownItem, itemId, durability);
        system.clearRun(delayDrop);
    }, 4);
}
export function dropWeapon(itemEntity, itemId, durability) {
    try {
        const newItem = new ItemStack(itemId, 1);
        const newDurability = newItem.getComponent(ItemComponentTypes.Durability);
        newDurability.damage = durability;
        itemEntity.dimension.spawnItem(newItem, itemEntity.location);
        itemEntity.remove();
    }
    catch (e) {
        world.getDimension(itemEntity.dimension.id).playSound('random.break', itemEntity.location);
        itemEntity.remove();
    }
}
