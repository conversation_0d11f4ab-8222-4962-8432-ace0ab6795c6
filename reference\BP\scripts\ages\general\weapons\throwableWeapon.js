import { ItemComponentTypes, system, world } from "@minecraft/server";
import { getRandomDamage } from "ages/main";
export let throwableWeapons = new Set(['va:wooden_spear', 'va:stone_spear', 'va:bronze_spear', 'va:iron_spear']);
export let throwableWeaponEntities = new Set(['va:thrown_wooden_spear', 'va:thrown_stone_spear', 'va:thrown_bronze_spear', 'va:thrown_iron_spear']);
export function thrownWeaponsHandler(player, oldItem) {
    const playerLocation = player.location;
    const oldDurability = oldItem.getComponent(ItemComponentTypes.Durability).damage;
    const itemEntity = player.dimension.getEntities({
        families: ['throwable'],
        location: playerLocation,
        closest: 1,
        maxDistance: 8
    })[0];
    if (itemEntity) {
        itemEntity.setProperty('va:durability', oldDurability);
    }
}
export function damageWeapon(itemEntity, durability, damageAmount) {
    try {
        itemEntity.setProperty('va:durability', durability + damageAmount);
    }
    catch (e) {
        world.getDimension(itemEntity.dimension.id).playSound('random.break', itemEntity.location);
        itemEntity.remove();
    }
}
export function delayedDamage(thrownItem, damage) {
    system.waitTicks(2).finally(() => {
        const durability = thrownItem.getProperty('va:durability');
        damageWeapon(thrownItem, durability, damage);
    });
}
export function weaponHit(thrownItem, entityId) {
    let damage;
    switch (entityId) {
        case "va:thrown_wooden_spear":
            damage = getRandomDamage(15, 20);
            break;
        case "va:thrown_stone_spear":
            damage = getRandomDamage(10, 15);
            break;
        case "va:thrown_bronze_spear":
            damage = getRandomDamage(6, 10);
            break;
        case "va:thrown_iron_spear":
            damage = getRandomDamage(4, 6);
            break;
        default:
            return;
    }
    delayedDamage(thrownItem, damage);
}
