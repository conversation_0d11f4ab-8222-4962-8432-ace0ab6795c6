{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "va:ballista", "materials": {"default": "entity_alphatest"}, "textures": {"default": "textures/entity/war_machines/ballista"}, "geometry": {"default": "geometry.ballista"}, "animations": {"rotate": "animation.ballista.rotate", "base_pose": "animation.ballista.base_pose", "load": "animation.ballista.load", "fire": "animation.ballista.fire", "general": "controller.animation.ballista.general"}, "scripts": {"animate": ["rotate", "general"]}, "render_controllers": ["controller.render.default"], "sound_effects": {"load": "crossbow.loading.start"}, "spawn_egg": {"texture": "ballista", "texture_index": 0}}}}