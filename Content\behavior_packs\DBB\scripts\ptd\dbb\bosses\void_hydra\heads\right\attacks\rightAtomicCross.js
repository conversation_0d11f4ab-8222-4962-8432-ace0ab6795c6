import { EntityDamageCause, system } from "@minecraft/server";
import { VOID_HYDRA_ATTACK_DAMAGES } from "../../../../general_attacks/attackDamages";
import { fixedLenRaycast } from "../../../../../utilities/raycasts";
/**
 * Attack timing constants for right atomic cross attack
 */
const DAMAGE_TIMING = 60; // Apply damage at tick 60
const ANIMATION_TIME = 120; // Total animation time in ticks
const COOLDOWN_TIME = 20; // Cooldown time in ticks after the attack completes
/**
 * Attack configuration
 */
const ATTACK_CONFIG = {
    /** Range of the atomic cross attack */
    RANGE: 16,
    /** Width of the cross beams */
    BEAM_WIDTH: 3,
    /** Particle density for visual effects */
    PARTICLE_DENSITY: 8
};
/**
 * Executes the right atomic cross attack for the Void Hydra
 * Creates a cross-shaped energy blast that damages entities in its path
 *
 * @param voidHydra The void hydra entity
 */
export function executeRightAtomicCrossAttack(voidHydra) {
    // Apply damage at tick 60
    let damageTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_bb:dead");
            if (isDead) {
                system.clearRun(damageTiming);
                return;
            }
            if (voidHydra.getProperty("ptd_bb:attack") === "right_atomic_cross") {
                performAtomicCrossDamage(voidHydra);
            }
        }
        catch (error) {
            system.clearRun(damageTiming);
        }
    }, DAMAGE_TIMING);
    // Reset attack and start cooldown after animation completes
    let resetTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_bb:dead");
            if (isDead) {
                system.clearRun(resetTiming);
                return;
            }
            if (voidHydra.getProperty("ptd_bb:attack") === "right_atomic_cross") {
                voidHydra.triggerEvent("ptd_bb:reset_attack");
            }
        }
        catch (error) {
            system.clearRun(resetTiming);
        }
    }, ANIMATION_TIME);
    // End cooldown after cooldown time
    let cooldownTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_bb:dead");
            if (isDead) {
                system.clearRun(cooldownTiming);
                return;
            }
            voidHydra.setProperty("ptd_bb:cooling_down", false);
        }
        catch (error) {
            system.clearRun(cooldownTiming);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
}
/**
 * Performs the atomic cross damage in a cross pattern
 * @param voidHydra The void hydra entity
 */
function performAtomicCrossDamage(voidHydra) {
    try {
        const origin = voidHydra.location;
        const damage = VOID_HYDRA_ATTACK_DAMAGES.right_atomic_cross.damage;
        // Create cross pattern: horizontal and vertical beams
        const crossDirections = [
            { x: 1, y: 0, z: 0 }, // East
            { x: -1, y: 0, z: 0 }, // West
            { x: 0, y: 0, z: 1 }, // South
            { x: 0, y: 0, z: -1 } // North
        ];
        // For each direction of the cross
        crossDirections.forEach(direction => {
            // Use raycast utility to get beam positions
            const beamPositions = fixedLenRaycast(origin, direction, ATTACK_CONFIG.RANGE, 0.5);
            beamPositions.forEach((centerPos, index) => {
                // Create beam width by checking positions around the center
                for (let width = -ATTACK_CONFIG.BEAM_WIDTH; width <= ATTACK_CONFIG.BEAM_WIDTH; width++) {
                    let beamPos;
                    if (direction.x !== 0) {
                        // Horizontal beam (vary Z position for width)
                        beamPos = {
                            x: centerPos.x,
                            y: centerPos.y,
                            z: centerPos.z + width
                        };
                    }
                    else {
                        // Vertical beam (vary X position for width)
                        beamPos = {
                            x: centerPos.x + width,
                            y: centerPos.y,
                            z: centerPos.z
                        };
                    }
                    // Damage entities at this position
                    voidHydra.dimension
                        .getEntities({
                        location: beamPos,
                        maxDistance: 1.5,
                        excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
                        excludeFamilies: ["void_hydra", "boss"]
                    })
                        .forEach((entity) => {
                        entity.applyDamage(damage, {
                            cause: EntityDamageCause.entityAttack,
                            damagingEntity: voidHydra
                        });
                    });
                    // Spawn particles for visual effect (reduce density)
                    if (index % 4 === 0 && width === 0) { // Only spawn on center line and reduce frequency
                        voidHydra.dimension.spawnParticle("minecraft:dragon_breath_trail", beamPos);
                    }
                }
            });
        });
        // Spawn explosion particle at origin
        voidHydra.dimension.spawnParticle("minecraft:large_explosion", origin);
    }
    catch (error) {
        // Handle errors silently
    }
}
