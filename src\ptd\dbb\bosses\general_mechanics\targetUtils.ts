import { <PERSON>ti<PERSON>, GameMode, Vector3, Player } from "@minecraft/server";
import { getDistance } from "../../utilities/vector3";

/**
 * Acquires valid targets for boss entities
 * Uses a strict prioritization system where higher priority targets are always selected regardless of distance
 * Priority order: Players (0) > Boss<PERSON> (1) > Minions (3)
 *
 * As long as a higher priority target exists, it will always be returned regardless of distance.
 * Only when there are no valid targets in a priority level will the function check the next priority level.
 *
 * @param entity The boss entity instance
 * @param location Current position of the boss
 * @param searchDistance Maximum distance to search for targets (default: 32)
 * @param excludeFamily Family to exclude from target search (e.g., "necromancer", "piglin_champion")
 * @param frontThreshold Optional threshold to check if the target is in front of the boss
 * @returns The selected target entity or undefined if no valid target
 */
export function getTarget(
  entity: Entity,
  location: Vector3,
  searchDistance: number = 32,
  excludeFamilies: string[] = [],
  frontThreshold?: number
): Entity | undefined {
  if (entity.getProperty("ptd_bb:spawning") as boolean) return undefined;

  // Priority 0: Players
  const targetPlayers = entity.dimension.getPlayers({
    location,
    maxDistance: searchDistance,
    excludeGameModes: [GameMode.creative, GameMode.spectator]
  });

  // Priority 1: Bosses
  const targetBosses = entity.dimension.getEntities({
    location,
    maxDistance: searchDistance,
    families: ["boss"],
    excludeFamilies: excludeFamilies ?? []
  });

  // Priority 2: Minions
  const targetMinions = entity.dimension.getEntities({
    location,
    maxDistance: searchDistance,
    families: ["minion"],
    excludeFamilies: excludeFamilies ?? []
  });

  // Helper to check if in front (if threshold provided)
  const isInFront = (target: Entity) =>
    frontThreshold === undefined ? true : isEntityInFront(entity, target, frontThreshold);

  // Find the closest player
  let closestPlayer: Entity | undefined = undefined;
  let closestPlayerDistance = searchDistance;
  for (const player of targetPlayers) {
    if (!isInFront(player)) continue;
    const distance = getDistance(location, player.location);
    if (distance < closestPlayerDistance) {
      closestPlayer = player;
      closestPlayerDistance = distance;
    }
  }

  // Find the closest boss (not spawning or dead)
  let closestBoss: Entity | undefined = undefined;
  let closestBossDistance = searchDistance;
  for (const boss of targetBosses) {
    const isSpawning = boss.getProperty("ptd_bb:spawning") as boolean;
    const isDead = boss.getProperty("ptd_bb:dead") as boolean;
    if (!isSpawning && !isDead && isInFront(boss)) {
      const distance = getDistance(location, boss.location);
      if (distance < closestBossDistance) {
        closestBoss = boss;
        closestBossDistance = distance;
      }
    }
  }

  // Find the closest minion
  let closestMinion: Entity | undefined = undefined;
  let closestMinionDistance = searchDistance;
  for (const minion of targetMinions) {
    if (!isInFront(minion)) continue;
    const distance = getDistance(location, minion.location);
    if (distance < closestMinionDistance) {
      closestMinion = minion;
      closestMinionDistance = distance;
    }
  }

  // Strict prioritization
  if (closestPlayer) {
    return closestPlayer;
  } else if (closestBoss) {
    return closestBoss;
  } else if (closestMinion) {
    return closestMinion;
  } else {
    return undefined;
  }
}

/**
 * Checks if an entity is in front of another entity.
 * @param target The entity whose view direction is used
 * @param entity The entity to check
 * @param threshold The dot product threshold (default: 0.5)
 * @returns True if the entity is in front, false otherwise
 */

// For aoe damage
// Exclude players in creative/spectator mode, inanimate entities, and bosses
export function isEntityInFront(target: Entity, entity: Entity, threshold: number = 0.5) {
    const viewDirection = target.getViewDirection();
    const directionToEntity = {
        x: entity.location.x - target.location.x,
        y: entity.location.y - target.location.y,
        z: entity.location.z - target.location.z
    };
    const magnitude = Math.sqrt(directionToEntity.x ** 2 + directionToEntity.y ** 2 + directionToEntity.z ** 2);
    const normalizedDirection = {
        x: directionToEntity.x / magnitude,
        y: directionToEntity.y / magnitude,
        z: directionToEntity.z / magnitude
    };
    const dotProduct = viewDirection.x * normalizedDirection.x + viewDirection.z * normalizedDirection.z;
    return dotProduct > threshold;
}

/**
 * Filters out invalid targets for the boss entity.
 * Excludes the source entity, players in creative/spectator mode, inanimate entities, and projectiles.
 *
 * @param sourceEntity The source entity (e.g., the boss)
 * @param entity The target entity to check
 * @returns True if the target is valid, false otherwise
 */
export const filterValidTargets = (sourceEntity: Entity) => (entity: Entity) => {
    if (entity === sourceEntity) return false;

    if (entity instanceof Player) {
        const gameMode = entity.getGameMode();
        if (gameMode === "creative" || gameMode === "spectator") return false;
    }

    const typeFamilyComponent = entity.getComponent("minecraft:type_family");
    if (typeFamilyComponent && typeFamilyComponent.hasTypeFamily('inanimate')) return false;

    return !entity.hasComponent("minecraft:projectile");
};