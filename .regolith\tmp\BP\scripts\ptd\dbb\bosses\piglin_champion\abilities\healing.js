import { EntityComponentTypes, GameMode, Player, system } from "@minecraft/server";
import { getDistance } from "../../../utilities/vector3";
import { executeUpchuckAttack } from "../attacks/upchuck";
/**
 * Attack timing constants for healing ability phases
 */
const HEALING_START_TIMING = 43; // Start progressive healing at tick 43
const KNOCKBACK_START_TIMING = 130; // Start continuous knockback at tick 130
/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 158;
/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;
/**
 * Executes the healing ability for the Piglin Champion using the new timing system
 * Uses localized runTimeout for healing phases, reset, and cooldown
 *
 * @param piglinChampion The piglin champion entity
 */
export function executeHealingAbility(piglinChampion) {
    // Phase 1: Start progressive healing at tick 43
    let healingTiming = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(healingTiming);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "healing") {
                // Get health component
                const healthComponent = piglinChampion.getComponent(EntityComponentTypes.Health);
                const maxHealth = healthComponent?.defaultValue || 0;
                // Calculate total heal amount (10% of max health)
                const totalHealAmount = Math.ceil(maxHealth * 0.1);
                // Start progressive healing
                startProgressiveHealing(piglinChampion, totalHealAmount);
            }
        }
        catch (error) {
            system.clearRun(healingTiming);
        }
    }, HEALING_START_TIMING);
    // Phase 2: Start continuous knockback at tick 130
    let knockbackTiming = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(knockbackTiming);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "healing") {
                startContinuousKnockback(piglinChampion);
            }
        }
        catch (error) {
            system.clearRun(knockbackTiming);
        }
    }, KNOCKBACK_START_TIMING);
    // Trigger upchuck attack after healing animation completes
    let triggerUpchuck = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(triggerUpchuck);
                return;
            }
            // Set attack to upchuck and execute it immediately
            piglinChampion.setProperty("ptd_dbb:attack", "upchuck");
            executeUpchuckAttack(piglinChampion);
            system.clearRun(triggerUpchuck);
        }
        catch (error) {
            system.clearRun(triggerUpchuck);
        }
    }, ANIMATION_TIME);
    // Wait for cooldown, then set cooldown property to false to select the next attack
    let cooldown = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldown);
                return;
            }
            piglinChampion.setProperty("ptd_dbb:cooling_down", false);
            system.clearRun(cooldown);
        }
        catch (error) {
            system.clearRun(cooldown);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
    return;
}
/**
 * Applies continuous knockback to nearby entities from the roar event until the healing sequence ends
 *
 * @param piglinChampion The piglin champion entity
 */
function startContinuousKnockback(piglinChampion) {
    // Calculate the duration of continuous knockback (from tick 130 to tick 145)
    const knockbackDuration = 15; // 15 ticks of knockback (145 - 130)
    const knockbackRadius = 8;
    let ticksElapsed = 0;
    // Apply knockback every tick for the duration
    const knockbackInterval = system.runInterval(() => {
        try {
            // Check if the entity is still valid and still in healing state
            const attack = piglinChampion.getProperty("ptd_dbb:attack");
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (attack !== "healing" || isDead || ticksElapsed >= knockbackDuration) {
                system.clearRun(knockbackInterval);
                return;
            }
            // Use the piglin's location as the origin for the knockback
            const originPos = piglinChampion.location;
            // Apply knockback to all entities within the radius
            piglinChampion.dimension.getEntities({
                location: originPos,
                maxDistance: knockbackRadius,
                excludeFamilies: ["piglin_champion", "piglin", "rock"]
            }).forEach((entity) => {
                // Use piglin's direction for knockback
                // Create 2D points (same y-coordinate) to calculate horizontal distance
                const point1 = { x: entity.location.x, y: 0, z: entity.location.z };
                const point2 = { x: originPos.x, y: 0, z: originPos.z };
                const distance = getDistance(point1, point2);
                if (distance > 0) {
                    // Calculate direction from piglin to entity
                    const dx = entity.location.x - originPos.x;
                    const dz = entity.location.z - originPos.z;
                    // Normalize direction
                    const length = Math.sqrt(dx * dx + dz * dz);
                    const nx = dx / length;
                    const nz = dz / length;
                    // Use slightly reduced knockback strength for continuous knockback
                    // to prevent players from being pushed too far away
                    const horizontalStrength = 3.5; // Half the strength of the initial roar
                    const verticalStrength = 0.4;
                    try {
                        // Try to apply knockback first
                        if (entity instanceof Player) {
                            const gameMode = entity.getGameMode();
                            if (gameMode === GameMode.survival || gameMode === GameMode.adventure) {
                                entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                            }
                        }
                        else {
                            entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                        }
                    }
                    catch (e) {
                        // Fallback to applyImpulse if applyKnockback fails
                        const impulse = {
                            x: nx * horizontalStrength,
                            y: verticalStrength,
                            z: nz * horizontalStrength
                        };
                        entity.applyImpulse(impulse);
                    }
                }
            });
            ticksElapsed++;
        }
        catch (e) {
            // Entity might have been removed
            system.clearRun(knockbackInterval);
        }
    }, 1); // Run every tick
}
/**
 * Applies healing progressively over time between ticks 43 and 88
 *
 * @param piglinChampion The piglin champion entity
 * @param totalHealAmount The total amount to heal
 */
function startProgressiveHealing(piglinChampion, totalHealAmount) {
    // Calculate healing parameters
    const healingDuration = 88 - HEALING_START_TIMING; // 45 ticks of healing
    const healPerTick = totalHealAmount / healingDuration;
    let ticksElapsed = 0;
    // Apply healing progressively
    const healingInterval = system.runInterval(() => {
        try {
            // Check if the entity is still valid and still in healing state
            const attack = piglinChampion.getProperty("ptd_dbb:attack");
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (attack !== "healing" || isDead || ticksElapsed >= healingDuration) {
                system.clearRun(healingInterval);
                return;
            }
            // Get health component
            const healthComponent = piglinChampion.getComponent(EntityComponentTypes.Health);
            if (!healthComponent) {
                system.clearRun(healingInterval);
                return;
            }
            // Get current health
            const currentHealth = healthComponent.currentValue;
            const maxHealth = healthComponent.defaultValue;
            // Calculate new health with progressive healing
            const newHealth = Math.min(currentHealth + healPerTick, maxHealth);
            // Apply healing
            healthComponent.setCurrentValue(newHealth);
            // Visual feedback for healing (optional)
            piglinChampion.dimension.spawnParticle("minecraft:heart_particle", {
                x: piglinChampion.location.x,
                y: piglinChampion.location.y + 2,
                z: piglinChampion.location.z
            });
            ticksElapsed++;
        }
        catch (e) {
            // Entity might have been removed
            system.clearRun(healingInterval);
        }
    }, 1); // Run every tick
}
