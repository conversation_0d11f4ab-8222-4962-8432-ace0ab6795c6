import { system, world, ItemComponentTypes, GameMode, Player, EntityComponentTypes, EquipmentSlot } from "@minecraft/server";
import { throwableWeaponEntities, throwableWeapons, thrownWeaponsHandler, weaponHit } from "./general/weapons/throwableWeapon";
import { bucketMechanicsBlockFill, bucketMechanicsBlockEmpty } from "./general/items/bucket";
import { axeInteractables, axes, damageToolOnUse, hoeInteractables, hoes, pickaxes, shovelInteractables, shovels } from "./general/tools/tools";
import { arrows } from "./general/weapons/arrow";
import { dropArrow, dropWeaponWithNewDurability } from "./general/weapons/general";
import { fermentationBarrelFermenting, fermentationBarrelInteract } from "./general/blocks/fermentationBarrel";
import { drunkThreshold, hitWithBeerGlass, onConsumeBeverage } from "./general/items/beverages";
import { controlledMachines, controlMachine, fireMachine } from "./general/war_machines/general";
world.beforeEvents.worldInitialize.subscribe(({ itemComponentRegistry, blockComponentRegistry }) => {
    blockComponentRegistry.registerCustomComponent('va:ferment', {
        onPlayerInteract: (ev) => {
            const player = ev.player;
            const block = ev.block;
            const blockId = block.type.id;
            if (blockId === 'va:fermentation_barrel') {
                fermentationBarrelInteract(player, block);
            }
        },
        onTick: (ev) => {
            fermentationBarrelFermenting(ev.block);
        }
    });
    itemComponentRegistry.registerCustomComponent('va:fill', {
        onUseOn(ev) {
            const player = ev.source;
            const block = ev.block;
            const blockId = block.type.id;
            if (blockId === 'minecraft:water') {
                bucketMechanicsBlockFill(player, block);
            }
            else {
                checkSurroundingBlocks(player, block);
            }
        }
    });
    itemComponentRegistry.registerCustomComponent('va:on_consume', {
        onConsume(ev) {
            const player = ev.source;
            const beverage = ev.itemStack.type.id;
            onConsumeBeverage(player, beverage);
        }
    });
    itemComponentRegistry.registerCustomComponent('va:empty', {
        onUseOn(ev) {
            const player = ev.source;
            const itemId = ev.itemStack.type.id.replace('_water', '');
            const block = ev.block;
            bucketMechanicsBlockEmpty(player, itemId, block);
        }
    });
    itemComponentRegistry.registerCustomComponent('va:drink', {
        onConsume(ev) {
            const player = ev.source;
            player.getEffects().forEach(effect => {
                const effectId = effect.typeId;
                player.removeEffect(effectId);
            });
        }
    });
    itemComponentRegistry.registerCustomComponent('va:use_shovel', {
        onUseOn(ev) {
            const player = ev.source;
            const item = ev.itemStack;
            const block = ev.block;
            const blockId = block.type.id;
            if (shovelInteractables.has(blockId)) {
                damageToolOnUse(player, item, block, false);
            }
        }
    });
    itemComponentRegistry.registerCustomComponent('va:use_axe', {
        onUseOn(ev) {
            const player = ev.source;
            const item = ev.itemStack;
            const block = ev.block;
            const blockId = block.type.id;
            if (axeInteractables.has(blockId)) {
                damageToolOnUse(player, item, block, false);
            }
        }
    });
    itemComponentRegistry.registerCustomComponent('va:use_hoe', {
        onUseOn(ev) {
            const player = ev.source;
            const item = ev.itemStack;
            const block = ev.block;
            const blockId = block.type.id;
            if (hoeInteractables.has(blockId)) {
                damageToolOnUse(player, item, block, false);
            }
        }
    });
});
world.afterEvents.playerLeave.subscribe(data => {
    const playerId = data.playerId;
    drunkThreshold.delete(playerId);
});
world.afterEvents.entityLoad.subscribe(data => {
    const entity = data.entity;
    const entityId = entity.typeId;
    if (entityId === 'va:ballista') {
        entity.resetProperty('va:controlled');
    }
});
world.afterEvents.entityHitEntity.subscribe(data => {
    const source = data.damagingEntity;
    const entityHit = data.hitEntity;
    if (source instanceof Player) {
        const equippable = source.getComponent(EntityComponentTypes.Equippable);
        const mainHand = equippable.getEquipmentSlot(EquipmentSlot.Mainhand);
        const mainHandItemId = mainHand.getItem().type.id;
        if (mainHandItemId === 'va:beer_glass_empty') {
            hitWithBeerGlass(source, entityHit);
        }
    }
});
world.afterEvents.entityDie.subscribe(data => {
    const entity = data.deadEntity;
    if (entity instanceof Player) {
        drunkThreshold.delete(entity.id);
    }
});
world.beforeEvents.playerBreakBlock.subscribe(data => {
    const player = data.player;
    const gameMode = player.getGameMode();
    const item = data.itemStack;
    const itemTypeId = item?.type.id;
    const hasSilkTouch = item?.getComponent(ItemComponentTypes.Enchantable)?.hasEnchantment('minecraft:silk_touch');
    const block = data.block;
    const blockLocation = block.center();
    if (gameMode !== GameMode.creative) {
        if (!hasSilkTouch) {
            if (shovels.has(itemTypeId) || axes.has(itemTypeId) || hoes.has(itemTypeId) || pickaxes.has(itemTypeId)) {
                let run = system.run(() => {
                    data.cancel = true;
                    damageToolOnUse(player, item, block, true);
                    system.clearRun(run);
                });
            }
            let delayBreak = system.runTimeout(() => {
                player.runCommand(`setblock ${blockLocation.x} ${blockLocation.y} ${blockLocation.z} minecraft:air [] destroy`);
                system.clearRun(delayBreak);
            });
        }
        else {
            if (shovels.has(itemTypeId) || axes.has(itemTypeId) || hoes.has(itemTypeId) || pickaxes.has(itemTypeId)) {
                let run = system.run(() => {
                    damageToolOnUse(player, item, block, true);
                    system.clearRun(run);
                });
            }
        }
    }
});
world.afterEvents.itemReleaseUse.subscribe(data => {
    const item = data.itemStack;
    const itemTypeId = item.type.id;
    const duration = data.useDuration;
    const player = data.source;
    if (throwableWeapons.has(itemTypeId) && duration >= 10) {
        thrownWeaponsHandler(player, item);
    }
});
world.afterEvents.dataDrivenEntityTrigger.subscribe(async (data) => {
    const event = data.eventId;
    const entity = data.entity;
    const entityId = entity.typeId;
    if (event === 'va:drop') {
        if (throwableWeaponEntities.has(entityId)) {
            dropWeaponWithNewDurability(entity, entityId);
        }
        else if (arrows.has(entityId)) {
            dropArrow(entity, entityId);
        }
    }
    else if (event === 'va:hit_once') {
        weaponHit(entity, entityId);
    }
    else if (event === 'va:loaded' || event === 'va:control') {
        await system.waitTicks(1);
        controlMachine(entity);
    }
    else if (event === 'va:fire') {
        fireMachine(entity, entityId);
    }
}, { eventTypes: ['va:drop', 'va:hit_once', 'va:loaded', 'va:control', 'va:fire'] });
world.afterEvents.playerInteractWithEntity.subscribe(async (data) => {
    const player = data.player;
    const entity = data.target;
    const entityId = entity.typeId;
    if (entityId === 'va:ballista') {
        if (!controlledMachines.has(player.id)) {
            await system.waitTicks(1);
            const isLoading = entity.getProperty('va:loading');
            const isLoaded = entity.getProperty('va:loaded');
            if (isLoading || isLoaded) {
                controlledMachines.set(player.id, entity);
            }
        }
    }
});
world.afterEvents.projectileHitBlock.subscribe(data => {
    const projectile = data.projectile;
    const projectileHitBlock = projectile.typeId;
    if (throwableWeaponEntities.has(projectileHitBlock)) {
        try {
            projectile.triggerEvent('va:hit_block');
        }
        catch (e) { }
    }
});
world.afterEvents.projectileHitEntity.subscribe(data => {
    const entity = data.projectile;
    const entityTypeId = entity.typeId;
    if (throwableWeaponEntities.has(entityTypeId)) {
        try {
            entity.triggerEvent('va:hit_entity');
        }
        catch (e) { }
    }
});
export function getRandomDamage(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}
function checkSurroundingBlocks(player, block) {
    const directions = ['above', 'below', 'east', 'west'];
    for (const direction of directions) {
        const adjacentBlock = block[direction]();
        const adjacentBlockId = adjacentBlock.type.id;
        if (adjacentBlockId === 'minecraft:water') {
            bucketMechanicsBlockFill(player, adjacentBlock);
            return;
        }
    }
    return;
}
