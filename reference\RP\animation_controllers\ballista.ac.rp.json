{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ballista.general": {"states": {"default": {"animations": ["base_pose"], "transitions": [{"loading": "q.property('va:loading') == true || q.property('va:loaded') == true"}], "blend_transition": 0.1}, "loading": {"animations": ["load"], "transitions": [{"firing": "q.property('va:firing') == true"}], "blend_transition": 0.1}, "firing": {"animations": ["fire"], "transitions": [{"default": "q.all_animations_finished"}]}}}}}