{"format_version": "1.10.0", "minecraft:attachable": {"description": {"identifier": "va:stone_spear", "min_engine_version": "1.8.0", "materials": {"default": "entity_alphatest", "enchanted": "entity_alphatest_glint"}, "textures": {"default": "textures/attachables/spears/stone_spear", "enchanted": "textures/misc/enchanted_item_glint"}, "geometry": {"default": "geometry.spear_general"}, "animations": {"wield": "controller.animation.trident.wield", "wield_first_person": "animation.trident.wield_first_person", "wield_first_person_raise": "animation.trident.wield_first_person_raise", "wield_first_person_raise_shake": "animation.trident.wield_first_person_raise_shake", "wield_first_person_riptide": "animation.trident.wield_first_person_riptide", "wield_third_person": "animation.trident.wield_third_person", "wield_third_person_raise": "animation.trident.wield_third_person_raise"}, "scripts": {"pre_animation": ["variable.charge_amount = math.clamp((query.main_hand_item_max_duration - (query.main_hand_item_use_duration - query.frame_alpha + 1.0)) / 10.0, 0.0, 1.0f);"], "animate": ["wield"]}, "render_controllers": ["controller.render.item_default"]}}}