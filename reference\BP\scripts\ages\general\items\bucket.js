import { EntityComponentTypes, EquipmentSlot, ItemStack, system } from "@minecraft/server";
export function bucketMechanicsBlockFill(player, block) {
    const location = player.location;
    const blockLocation = block.center();
    const mainHand = player.getComponent(EntityComponentTypes.Equippable).getEquipmentSlot(EquipmentSlot.Mainhand);
    const mainhandItem = mainHand.getItem();
    const previousEmptyBucketCount = mainHand.getItem().amount;
    if (previousEmptyBucketCount > 1) {
        const newEmptyBucketCount = new ItemStack(mainhandItem.type, previousEmptyBucketCount - 1);
        mainHand.setItem(newEmptyBucketCount);
    }
    else {
        mainHand.setItem(new ItemStack("minecraft:air", 1));
    }
    block.setType('minecraft:air');
    player.dimension.playSound('bucket.fill_water', blockLocation);
    const filledBucket = new ItemStack("va:clay_water_bucket", 1);
    let delayFill = system.runTimeout(() => {
        player.dimension.spawnItem(filledBucket, location);
        system.clearRun(delayFill);
    }, 1);
}
export function bucketMechanicsBlockEmpty(player, itemId, block) {
    const blockLocation = block.center();
    const mainHand = player.getComponent(EntityComponentTypes.Equippable).getEquipmentSlot(EquipmentSlot.Mainhand);
    mainHand.setItem(new ItemStack(itemId, 1));
    player.dimension.playSound('bucket.empty_water', blockLocation);
}
