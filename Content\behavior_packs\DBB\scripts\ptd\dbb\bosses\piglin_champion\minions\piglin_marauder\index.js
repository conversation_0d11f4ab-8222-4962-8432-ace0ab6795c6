import { executeSlamAttack } from "./attacks/slam";
import { executeSweepAttack } from "./attacks/sweep";
/**
 * The total animation time in ticks / length of the slowness effect for slam attack
 */
const SLAM_ANIMATION_TIME = 30; // 1.5 seconds * 20 ticks = 30 ticks
/**
 * The total animation time in ticks / length of the slowness effect for sweep attack
 */
const SWEEP_ANIMATION_TIME = 28; // 1.375 seconds * 20 ticks = 28 ticks
/**
 * Handles all mechanics for the Piglin Marauder
 * This includes attack selection, attack execution, and cooldown management
 * Similar to zombie brute but with two different attack types
 *
 * @param piglinMarauder The piglin marauder entity
 */
export function piglinMarauderMechanics(piglinMarauder) {
    // Skip if entity is not valid
    try {
        if (!piglinMarauder)
            return;
        // Skip if entity is spawning or dead
        const isSpawning = piglinMarauder.getProperty("ptd_bb:spawning");
        const isDead = piglinMarauder.getProperty("ptd_bb:dead");
        if (isSpawning || isDead)
            return;
        // Get the current attack type
        const attackType = piglinMarauder.getProperty("ptd_bb:attack");
        // Apply slowness effect to prevent movement during the attack
        // Use different durations based on attack type
        if (attackType === "slam") {
            piglinMarauder.addEffect("minecraft:slowness", SLAM_ANIMATION_TIME, { amplifier: 250, showParticles: false });
            executeSlamAttack(piglinMarauder);
        }
        else if (attackType === "sweep") {
            piglinMarauder.addEffect("minecraft:slowness", SWEEP_ANIMATION_TIME, { amplifier: 250, showParticles: false });
            executeSweepAttack(piglinMarauder);
        }
    }
    catch (e) {
        return;
    }
    return;
}
