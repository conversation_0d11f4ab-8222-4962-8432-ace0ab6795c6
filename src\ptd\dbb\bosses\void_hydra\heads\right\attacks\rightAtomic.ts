import { Entity, EntityDamageCause, Vector3, system } from "@minecraft/server";
import { VOID_HYDRA_ATTACK_DAMAGES } from "../../../../general_attacks/attackDamages";
import { getTarget } from "../../../../general_mechanics/targetUtils";
import { fixedLenRaycast } from "../../../../../utilities/raycasts";

/**
 * Attack timing constants for right atomic attack
 */
const DAMAGE_START_TIMING = 11; // Start continuous damage at tick 11
const DAMAGE_END_TIMING = 34; // End continuous damage at tick 34
const ANIMATION_TIME = 66; // Total animation time in ticks
const COOLDOWN_TIME = 20; // Cooldown time in ticks after the attack completes

/**
 * Attack configuration
 */
const ATTACK_CONFIG = {
  /** Range of the atomic beam */
  RANGE: 48,
  /** Width of the beam */
  BEAM_WIDTH: 4,
  /** Total sweep angle in degrees */
  SWEEP_ANGLE: 90,
  /** Number of particles to spawn */
  PARTICLE_COUNT: 8
};

/**
 * Executes the right atomic attack for the Void Hydra Right Head
 * Creates a sweeping beam attack that continuously damages entities between ticks 11-34
 * The beam sweeps horizontally from left to right in a 90-degree arc
 *
 * @param rightHead The void hydra right head entity
 */
export function executeRightHeadAtomicAttack(rightHead: Entity): void {
  let swipeInterval: number;

  // Start continuous swipe damage at tick 11
  let damageStartTiming = system.runTimeout(() => {
    try {
      const isDead = rightHead.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(damageStartTiming);
        return;
      }

      if (rightHead.getProperty("ptd_bb:attack") === "right_atomic") {
        // Get initial target position when attack starts - this position will be used throughout the attack
        const initialTarget = getTarget(rightHead, rightHead.location, 32, ["void_hydra", "void_hydra_head"]);
        const initialTargetPosition: Vector3 | null = initialTarget
          ? {
              x: initialTarget.location.x,
              y: initialTarget.location.y + 1.5, // Aim slightly above target
              z: initialTarget.location.z
            }
          : null;

        // Start continuous swipe effect
        let currentTick = DAMAGE_START_TIMING;
        swipeInterval = system.runInterval(() => {
          try {
            const isDead = rightHead.getProperty("ptd_bb:dead") as boolean;
            if (isDead || rightHead.getProperty("ptd_bb:attack") !== "right_atomic") {
              system.clearRun(swipeInterval);
              return;
            }

            if (currentTick <= DAMAGE_END_TIMING) {
              performRightHeadAtomicBeam(rightHead, currentTick, initialTargetPosition);
              currentTick++;
            } else {
              system.clearRun(swipeInterval);
            }
          } catch (error) {
            system.clearRun(swipeInterval);
          }
        }, 1);
      }
    } catch (error) {
      system.clearRun(damageStartTiming);
    }
  }, DAMAGE_START_TIMING);

  // Reset attack and start cooldown after animation completes
  let resetTiming = system.runTimeout(() => {
    try {
      const isDead = rightHead.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(resetTiming);
        if (swipeInterval) system.clearRun(swipeInterval);
        return;
      }

      if (rightHead.getProperty("ptd_bb:attack") === "right_atomic") {
        rightHead.triggerEvent("ptd_bb:reset_attack");
      }
    } catch (error) {
      system.clearRun(resetTiming);
    }
  }, ANIMATION_TIME);

  // End cooldown after cooldown time
  let cooldownTiming = system.runTimeout(() => {
    try {
      const isDead = rightHead.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(cooldownTiming);
        return;
      }

      rightHead.setProperty("ptd_bb:cooling_down", false);
    } catch (error) {
      system.clearRun(cooldownTiming);
    }
  }, ANIMATION_TIME + COOLDOWN_TIME);
}

/**
 * Performs the atomic beam damage in a sweeping pattern for the right head
 * @param rightHead The void hydra right head entity
 * @param currentTick The current tick in the attack sequence
 * @param targetPosition The initial target position to aim at (fixed when attack started)
 */
function performRightHeadAtomicBeam(rightHead: Entity, currentTick: number, targetPosition: Vector3 | null): void {
  try {
    // If no initial target position was saved, don't perform the attack
    if (!targetPosition) return;

    const viewDirection = rightHead.getViewDirection();
    const origin: Vector3 = {
      x: rightHead.location.x + viewDirection.x * 8,
      y: rightHead.location.y + 5,
      z: rightHead.location.z + viewDirection.z * 8
    }; // Origin positioned 2 blocks above and 8 blocks in front of right head
    const damage = VOID_HYDRA_ATTACK_DAMAGES.right_atomic.damage;

    // Calculate target direction using the fixed initial target position
    const targetDirection = {
      x: targetPosition.x - origin.x,
      y: targetPosition.y - origin.y,
      z: targetPosition.z - origin.z
    };

    // Normalize target direction
    const magnitude = Math.sqrt(targetDirection.x ** 2 + targetDirection.y ** 2 + targetDirection.z ** 2);
    if (magnitude === 0) return;

    targetDirection.x /= magnitude;
    targetDirection.y /= magnitude;
    targetDirection.z /= magnitude;

    // Calculate sweep progress (0 to 1)
    const totalTicks = DAMAGE_END_TIMING - DAMAGE_START_TIMING + 1;
    const linearProgress = ((currentTick - DAMAGE_START_TIMING) / (totalTicks - 1)) * 1.6;

    // Apply quadratic easing for gradually increasing sweep speed (starts slow, accelerates)
    const easedProgress = linearProgress * linearProgress;

    // Calculate sweep angle (-45 to +45 degrees for 90 degree total sweep)
    const sweepAngleRad = (easedProgress - 0.5) * ((ATTACK_CONFIG.SWEEP_ANGLE * Math.PI) / 180);

    // Calculate perpendicular vector for horizontal sweep
    const perpendicular = {
      x: -targetDirection.z,
      y: 0,
      z: targetDirection.x
    };

    // Calculate beam direction by rotating target direction by sweep angle
    const beamDirection = {
      x: targetDirection.x * Math.cos(sweepAngleRad) + perpendicular.x * Math.sin(sweepAngleRad),
      y: targetDirection.y,
      z: targetDirection.z * Math.cos(sweepAngleRad) + perpendicular.z * Math.sin(sweepAngleRad)
    };

    // Use raycast to get beam positions with efficient step size
    const stepSize = ATTACK_CONFIG.BEAM_WIDTH / 2; // Use half beam width for good coverage
    const beamPositions = fixedLenRaycast(origin, beamDirection, ATTACK_CONFIG.RANGE, stepSize);
    const entitiesHit = new Set<Entity>();

    // Process each position along the beam
    beamPositions.forEach((beamPos) => {
      // Check for entities at this beam position using beam width as max distance
      rightHead.dimension
        .getEntities({
          location: beamPos,
          maxDistance: ATTACK_CONFIG.BEAM_WIDTH,
          excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
          excludeFamilies: ["void_hydra", "void_hydra_head"]
        })
        .forEach((entity) => {
          // Avoid hitting the same entity multiple times in one tick
          if (!entitiesHit.has(entity)) {
            entitiesHit.add(entity);

            entity.applyDamage(damage, {
              cause: EntityDamageCause.entityAttack,
              damagingEntity: rightHead
            });

            // Apply knockback along beam direction
            try {
              entity.applyKnockback(beamDirection.x, beamDirection.z, 0.6, 0.2);
            } catch (knockbackError) {
              // Fallback to applyImpulse if applyKnockback fails
              try {
                const impulse = {
                  x: beamDirection.x * 0.3,
                  y: 0.1,
                  z: beamDirection.z * 0.3
                };
                entity.applyImpulse(impulse);
              } catch (impulseError) {
                // Ignore if both methods fail
              }
            }
          }
        });

      // Spawn particles for visual effect (reduce density)
      rightHead.dimension.spawnParticle("minecraft:crop_growth_emitter", beamPos);
    });

    // Spawn beam origin effect
    if (currentTick % 3 === 0) {
      rightHead.dimension.spawnParticle("minecraft:large_explosion", origin);
    }
  } catch (error) {
    // Handle errors silently
  }
}
