import { Enti<PERSON>, EntityDamageCause, Vector3, system } from "@minecraft/server";
import { VOID_HYDRA_ATTACK_DAMAGES } from "../../../../general_attacks/attackDamages";
import { getTarget } from "../../../../general_mechanics/targetUtils";

/**
 * Attack timing constants for mid singularity attack
 */
const SINGULARITY_START_TIMING = 40; // Start singularity at tick 40
const SINGULARITY_END_TIMING = 160; // End singularity at tick 160
const ANIMATION_TIME = 200; // Total animation time in ticks
const COOLDOWN_TIME = 20; // Cooldown time in ticks after the attack completes

/**
 * Attack configuration
 */
const ATTACK_CONFIG = {
  /** Maximum pull range */
  MAX_RANGE: 24,
  /** Core damage radius */
  CORE_RADIUS: 4,
  /** Pull strength */
  PULL_STRENGTH: 0.2,
  /** Damage interval in ticks */
  DAMAGE_INTERVAL: 15
};

/**
 * Executes the mid singularity attack for the Void Hydra
 * Creates a black hole effect that pulls entities in and deals massive damage at the center
 *
 * @param voidHydra The void hydra entity
 */
export function executeMidSingularityAttack(voidHydra: Entity): void {
  let singularityInterval: number;

  // Start singularity effect at tick 40
  let singularityStartTiming = system.runTimeout(() => {
    try {
      const isDead = voidHydra.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(singularityStartTiming);
        return;
      }

      if (voidHydra.getProperty("ptd_bb:attack") === "mid_singularity") {
        // Start continuous singularity effect
        let currentTick = SINGULARITY_START_TIMING;
        singularityInterval = system.runInterval(() => {
          try {
            const isDead = voidHydra.getProperty("ptd_bb:dead") as boolean;
            if (isDead || voidHydra.getProperty("ptd_bb:attack") !== "mid_singularity") {
              system.clearRun(singularityInterval);
              return;
            }

            if (currentTick <= SINGULARITY_END_TIMING) {
              performSingularityEffect(voidHydra, currentTick);
              currentTick++;
            } else {
              system.clearRun(singularityInterval);
            }
          } catch (error) {
            system.clearRun(singularityInterval);
          }
        }, 1);
      }
    } catch (error) {
      system.clearRun(singularityStartTiming);
    }
  }, SINGULARITY_START_TIMING);

  // Reset attack and start cooldown after animation completes
  let resetTiming = system.runTimeout(() => {
    try {
      const isDead = voidHydra.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(resetTiming);
        if (singularityInterval) system.clearRun(singularityInterval);
        return;
      }

      if (voidHydra.getProperty("ptd_bb:attack") === "mid_singularity") {
        voidHydra.triggerEvent("ptd_bb:reset_attack");
      }
    } catch (error) {
      system.clearRun(resetTiming);
    }
  }, ANIMATION_TIME);

  // End cooldown after cooldown time
  let cooldownTiming = system.runTimeout(() => {
    try {
      const isDead = voidHydra.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(cooldownTiming);
        return;
      }

      voidHydra.setProperty("ptd_bb:cooling_down", false);
    } catch (error) {
      system.clearRun(cooldownTiming);
    }
  }, ANIMATION_TIME + COOLDOWN_TIME);
}

/**
 * Performs the singularity effect for a specific tick
 * @param voidHydra The void hydra entity
 * @param currentTick The current tick of the singularity effect
 */
function performSingularityEffect(voidHydra: Entity, currentTick: number): void {
  try {
    const target = getTarget(voidHydra, voidHydra.location, 32, ["void_hydra"]);
    if (!target) return;

    const singularityCenter: Vector3 = {
      x: target.location.x,
      y: target.location.y + 2, // Slightly above target
      z: target.location.z
    };
    
    const damage = VOID_HYDRA_ATTACK_DAMAGES.mid_singularity.damage;

    // Get all entities within range
    const entities = voidHydra.dimension.getEntities({
      location: singularityCenter,
      maxDistance: ATTACK_CONFIG.MAX_RANGE,
      excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
      excludeFamilies: ["void_hydra", "boss"]
    });

    entities.forEach((entity) => {
      const distance = Math.sqrt(
        (entity.location.x - singularityCenter.x) ** 2 +
        (entity.location.y - singularityCenter.y) ** 2 +
        (entity.location.z - singularityCenter.z) ** 2
      );

      // Calculate pull strength based on distance (stronger pull when closer)
      const pullMultiplier = Math.max(0.1, 1 - (distance / ATTACK_CONFIG.MAX_RANGE));
      const currentPullStrength = ATTACK_CONFIG.PULL_STRENGTH * pullMultiplier;

      // Calculate pull direction towards the singularity center
      const pullDirection = {
        x: singularityCenter.x - entity.location.x,
        y: singularityCenter.y - entity.location.y,
        z: singularityCenter.z - entity.location.z
      };

      // Normalize the direction
      const magnitude = Math.sqrt(pullDirection.x ** 2 + pullDirection.y ** 2 + pullDirection.z ** 2);
      if (magnitude > 0) {
        pullDirection.x = (pullDirection.x / magnitude) * currentPullStrength;
        pullDirection.y = (pullDirection.y / magnitude) * currentPullStrength * 0.7; // Reduced vertical pull
        pullDirection.z = (pullDirection.z / magnitude) * currentPullStrength;

        // Apply pull effect
        try {
          entity.applyImpulse(pullDirection);
        } catch (impulseError) {
          // Ignore if impulse fails
        }
      }

      // Apply damage to entities in the core radius at intervals
      if (distance <= ATTACK_CONFIG.CORE_RADIUS && 
          (currentTick - SINGULARITY_START_TIMING) % ATTACK_CONFIG.DAMAGE_INTERVAL === 0) {
        entity.applyDamage(damage, { 
          cause: EntityDamageCause.entityAttack, 
          damagingEntity: voidHydra 
        });
      }

      // Spawn pull particles around entities being affected
      if (currentTick % 5 === 0) {
        voidHydra.dimension.spawnParticle("minecraft:portal", entity.location);
      }
    });

    // Spawn singularity core effects
    if (currentTick % 3 === 0) {
      voidHydra.dimension.spawnParticle("minecraft:dragon_breath_trail", singularityCenter);
    }

    // Spawn swirling particles around the singularity
    if (currentTick % 2 === 0) {
      const time = (currentTick - SINGULARITY_START_TIMING) * 0.1;
      for (let i = 0; i < 8; i++) {
        const angle = (i / 8) * Math.PI * 2 + time;
        const radius = 6 + Math.sin(time * 2) * 2;
        const spiralPos = {
          x: singularityCenter.x + Math.cos(angle) * radius,
          y: singularityCenter.y + Math.sin(time * 3) * 2,
          z: singularityCenter.z + Math.sin(angle) * radius
        };
        
        voidHydra.dimension.spawnParticle("minecraft:soul_particle", spiralPos);
      }
    }
  } catch (error) {
    // Handle errors silently
  }
}
