import { getDistance } from "../../../../utilities/vector3";
import { getAvailableAttacks, updateAttackHistory, displayAttackHistory, SHORT_RANGE_ATTACKS, MEDIUM_RANGE_ATTACKS, LONG_RANGE_ATTACKS, UNREACHABLE_RANGE_ATTACKS } from "./attackTracker";
/**
 * Attack ranges for different attack types
 */
export const ATTACK_RANGES = {
    close: { min: 0, max: 16 },
    medium: { min: 16, max: 32 },
    long: { min: 32, max: 48 },
    unreachable: { min: 48, max: 64 }
};
/**
 * Selects an attack based on target distance
 * @param voidHydra The void hydra entity
 * @param target The target entity
 */
export function selectAttack(voidHydra, target) {
    // Check if the attack cooldown is 0
    const attackCooldown = voidHydra.getProperty("ptd_bb:cooling_down");
    if (attackCooldown) {
        return; // Don't select an attack if the cooldown is not 0
    }
    const distance = getDistance(voidHydra.location, target.location);
    let selectedAttack;
    // Short range (0-5 blocks): select from available attacks based on usage history
    if (distance >= ATTACK_RANGES.close.min && distance <= ATTACK_RANGES.close.max) {
        // Get available attacks based on usage history
        const availableAttacks = getAvailableAttacks(voidHydra, "short", SHORT_RANGE_ATTACKS);
        if (availableAttacks.length > 0) {
            // Randomly select one of the available attacks
            const randomIndex = Math.floor(Math.random() * availableAttacks.length);
            const attack = availableAttacks[randomIndex];
            selectedAttack = attack;
            // Trigger the attack event
            voidHydra.triggerEvent(`ptd_bb:${attack}_attack`);
            // Set the attack property
            voidHydra.setProperty("ptd_bb:attack", attack);
            // Update attack history
            updateAttackHistory(voidHydra, "short", attack);
            // Display attack history on the actionbar
            displayAttackHistory(voidHydra, "short");
        }
    }
    // Medium range (5-10 blocks): select from available attacks based on usage history
    else if (distance > ATTACK_RANGES.medium.min && distance <= ATTACK_RANGES.medium.max) {
        // Get available attacks based on usage history
        const availableAttacks = getAvailableAttacks(voidHydra, "medium", MEDIUM_RANGE_ATTACKS);
        if (availableAttacks.length > 0) {
            // Randomly select one of the available attacks
            const randomIndex = Math.floor(Math.random() * availableAttacks.length);
            const attack = availableAttacks[randomIndex];
            selectedAttack = attack;
            // Trigger the attack event
            voidHydra.triggerEvent(`ptd_bb:${attack}_attack`);
            // Set the attack property
            voidHydra.setProperty("ptd_bb:attack", attack);
            // Update attack history
            updateAttackHistory(voidHydra, "medium", attack);
            // Display attack history on the actionbar
            displayAttackHistory(voidHydra, "medium");
        }
    }
    // Long range (10-16 blocks): select from available attacks based on usage history
    else if (distance > ATTACK_RANGES.long.min && distance <= ATTACK_RANGES.long.max) {
        // Get available attacks based on usage history
        const availableAttacks = getAvailableAttacks(voidHydra, "long", LONG_RANGE_ATTACKS);
        if (availableAttacks.length > 0) {
            // Randomly select one of the available attacks
            const randomIndex = Math.floor(Math.random() * availableAttacks.length);
            const attack = availableAttacks[randomIndex];
            selectedAttack = attack;
            // Trigger the attack event
            voidHydra.triggerEvent(`ptd_bb:${attack}_attack`);
            // Set the attack property
            voidHydra.setProperty("ptd_bb:attack", attack);
            // Update attack history
            updateAttackHistory(voidHydra, "long", attack);
            // Display attack history on the actionbar
            displayAttackHistory(voidHydra, "long");
        }
    }
    // Unreachable range (16-32 blocks): select from available attacks based on usage history
    else if (distance > ATTACK_RANGES.unreachable.min && distance <= ATTACK_RANGES.unreachable.max) {
        // Get available attacks based on usage history
        const availableAttacks = getAvailableAttacks(voidHydra, "unreachable", UNREACHABLE_RANGE_ATTACKS);
        if (availableAttacks.length > 0) {
            // Randomly select one of the available attacks
            const randomIndex = Math.floor(Math.random() * availableAttacks.length);
            const attack = availableAttacks[randomIndex];
            selectedAttack = attack;
            // Trigger the attack event
            voidHydra.triggerEvent(`ptd_bb:${attack}_attack`);
            // Set the attack property
            voidHydra.setProperty("ptd_bb:attack", attack);
            // Update attack history
            updateAttackHistory(voidHydra, "unreachable", attack);
            // Display attack history on the actionbar
            displayAttackHistory(voidHydra, "unreachable");
        }
    }
}
