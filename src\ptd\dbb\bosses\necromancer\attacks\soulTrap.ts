import { Enti<PERSON>, EntityComponentTypes, EntityDamageCause, InputPermissionCate<PERSON><PERSON>, Player, Vector3 } from "@minecraft/server";
import { system } from "@minecraft/server";
import { NECROMANCER_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
import { getTarget } from "../../general_mechanics/targetUtils";

/**
 * Attack timing constants for soul trap attack phases
 */
const SOUL_TRAP_SPAWN_TIMING = 45; // Spawn soul trap at tick 45

/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 90; // Total attack duration is 90 ticks

/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;

/**
 * Configuration for the soul trap attack
 */
const SOUL_TRAP_CONFIG = {
  /** Duration for which player input permissions are disabled */
  STUN_DURATION: 79
};

/**
 * Executes the soul trap attack for the Necromancer using the new timing system
 * Uses localized runTimeout for soul trap spawn, reset, and cooldown
 *
 * @param necromancer The necromancer entity
 * @param target The target entity
 */
export function executeSoulTrapAttack(necromancer: Entity, target: Entity): void {
  // Check if the target is a void hydra head
  const isVoidHydraHead = target.getComponent(EntityComponentTypes.TypeFamily)?.hasTypeFamily("void_hydra_head");

  // If the target is a void hydra head, find a new target
  // This prevents the soul trap from being spawned on a void hydra head
  // Since teleporting the void hydra head would cause dismount the head
  if (isVoidHydraHead) {
    const newTarget = getTarget(necromancer, necromancer.location, 32, ["void_hydra_head", "necromancer"]);
    if (newTarget) {
      target = newTarget;
    } else {
      necromancer.setProperty("ptd_bb:attack", "none");
      necromancer.setProperty("ptd_bb:cooling_down", false);
      return;
    }
  }
  // Spawn soul trap at tick 45
  let soulTrapTiming = system.runTimeout(() => {
    try {
      const isDead = necromancer.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(soulTrapTiming);
        return;
      }

      if (necromancer.getProperty("ptd_bb:attack") === "soul_trap") {
        performSoulTrapAttack(necromancer, target);
      }
    } catch (error) {
      system.clearRun(soulTrapTiming);
    }
  }, SOUL_TRAP_SPAWN_TIMING);

  // Reset attack after animation completes
  let resetTiming = system.runTimeout(() => {
    try {
      const isDead = necromancer.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(resetTiming);
        return;
      }

      if (necromancer.getProperty("ptd_bb:attack") === "soul_trap") {
        necromancer.triggerEvent("ptd_bb:reset_attack");
      }
    } catch (error) {
      system.clearRun(resetTiming);
    }
  }, ANIMATION_TIME);

  // Set cooldown after reset
  let cooldownTiming = system.runTimeout(() => {
    try {
      const isDead = necromancer.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(cooldownTiming);
        return;
      }

      necromancer.setProperty("ptd_bb:cooling_down", false);
    } catch (error) {
      system.clearRun(cooldownTiming);
    }
  }, ANIMATION_TIME + COOLDOWN_TIME);
}

/**
 * Performs the actual soul trap attack logic
 * @param necromancer The necromancer entity
 * @param target The target entity
 */
async function performSoulTrapAttack(necromancer: Entity, target: Entity): Promise<void> {
  try {
    // Save the initial location of the target when the soul trap is spawned
    const initialLocation: Vector3 = { ...target.location };

    // Spawn the soul trap entity at the target's location
    const soulTrap = necromancer.dimension.spawnEntity("ptd_bb:soul_trap", initialLocation);
    necromancer.dimension.spawnParticle("ptd_bb:nec_soulhands1_01", initialLocation);

    // Set up position check and teleport functionality
    let positionCheckId: number | undefined;

    // Start a periodic check to handle entity positions
    positionCheckId = system.runInterval(() => {
      try {
        // Check if both the target and soul trap are still valid
        if (target && target.dimension && soulTrap && soulTrap.dimension) {
          // Use direct damage value instead of percentage
          const damage = NECROMANCER_ATTACK_DAMAGES.soul_drain.damage;
          target.applyDamage(damage, { cause: EntityDamageCause.entityAttack });

          if (target instanceof Player) {
            // For players: Update the soul trap position to match the player's current position
            soulTrap.teleport(target.location);
          } else {
            // For non-player entities: Teleport them back to the initial location
            target.teleport(initialLocation);
          }
        } else {
          // Either target or soul trap is no longer valid, clear the interval
          if (positionCheckId !== undefined) {
            system.clearRun(positionCheckId);
            positionCheckId = undefined;
          }
        }
      } catch (error) {
        // Clear the interval on error
        if (positionCheckId !== undefined) {
          system.clearRun(positionCheckId);
          positionCheckId = undefined;
        }
      }
    }, 1); // Check every tick

    // If the target is a player, disable their movement permissions
    if (target instanceof Player) {
      // Disable movement but allow camera movement
      target.inputPermissions.setPermissionCategory(InputPermissionCategory.Movement, false);
    }

    // Schedule restoration of permissions and cleanup after the stun duration
    let timeout = system.runTimeout(() => {
      try {
        // Clear the position check interval
        if (positionCheckId !== undefined) {
          system.clearRun(positionCheckId);
          positionCheckId = undefined;
        }

        // Check if the target is still valid
        if (target && target.dimension) {
          // Restore movement permissions if target is a player
          if (target instanceof Player) {
            target.inputPermissions.setPermissionCategory(InputPermissionCategory.Movement, true);
          }
        }
        system.clearRun(timeout);
      } catch (error) {
        // handle error silently
      }
    }, SOUL_TRAP_CONFIG.STUN_DURATION);
  } catch (error) {
    console.warn(`Error in soul trap attack: ${error}`);
  }
}
