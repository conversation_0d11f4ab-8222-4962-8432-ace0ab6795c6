{"compilerOptions": {"module": "ES2022", "target": "ES2023", "moduleResolution": "Node10", "removeComments": false, "strictFunctionTypes": true, "allowSyntheticDefaultImports": true, "noUnusedLocals": true, "pretty": true, "strictPropertyInitialization": true, "noUncheckedIndexedAccess": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "allowUnreachableCode": false, "allowUnusedLabels": false, "strict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "noImplicitAny": true, "strictNullChecks": true, "experimentalDecorators": true, "paths": {"@/*": ["*"]}, "baseUrl": "src", "rootDir": "src", "outDir": "Content/behavior_packs/BB/scripts"}, "include": ["src"], "exclude": ["node_modules", "dist", "build", "**/*.test.ts"]}