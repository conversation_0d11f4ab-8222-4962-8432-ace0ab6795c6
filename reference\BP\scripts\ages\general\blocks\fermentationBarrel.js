import { BlockPermutation, EntityComponentTypes, EquipmentSlot, ItemStack, system, } from "@minecraft/server";
export async function fermentationBarrelInteract(player, block) {
    const permutation = block.permutation;
    const isFermenting = permutation.getState('va:fermenting');
    const isReady = permutation.getState('va:ready_to_collect');
    const fermentedType = permutation.getState('va:ferment');
    const fermentedAmount = permutation.getState('va:fermented_items');
    const equippable = player.getComponent(EntityComponentTypes.Equippable);
    const mainHand = equippable.getEquipmentSlot(EquipmentSlot.Mainhand);
    const mainHandItem = mainHand.getItem();
    const mainHandItemTypeId = mainHandItem?.type.id;
    if (!isFermenting && !isReady) {
        const fermentations = {
            'minecraft:honeycomb': 'mead',
            'minecraft:glow_berries': 'wine',
            'minecraft:wheat': 'ale',
        };
        if (mainHandItemTypeId in fermentations) {
            if (mainHandItem.amount >= 15) {
                const fermentProduct = fermentations[mainHandItemTypeId];
                block.dimension.playSound('block.barrel.close', block.center());
                block.dimension.playSound('block.composter.fill', block.center());
                block.setPermutation(BlockPermutation.resolve(block.type.id, { 'va:fermenting': true, 'va:ferment': fermentProduct }));
                player.sendMessage(`Fermenting with §2${mainHandItemTypeId.split(':')[1]}§r to make ${fermentProduct}. Fermentation will take 3 days`);
                try {
                    mainHand.setItem(new ItemStack(mainHandItem.type.id, mainHandItem.amount - 15));
                }
                catch {
                    mainHand.setItem(new ItemStack('minecraft:air'));
                }
            }
            else {
                player.sendMessage('Not enough items to ferment. Items needed: §215§r');
            }
        }
        else {
            player.sendMessage('Items Accepted: §2Honeycomb§r, §2Glowberries§r, §2Wheat§r');
        }
    }
    else if (isFermenting && !isReady) {
        player.sendMessage('Currently fermenting');
    }
    else if (!isFermenting && isReady && fermentedAmount > 0) {
        if (mainHandItemTypeId === 'va:beer_glass_empty') {
            try {
                mainHand.setItem(new ItemStack(mainHandItem.type.id, mainHandItem.amount - 1));
                await system.waitTicks(1);
                player.dimension.spawnItem(new ItemStack(`va:beer_glass_${fermentedType}`), player.location);
            }
            catch {
                mainHand.setItem(new ItemStack(`va:beer_glass_${fermentedType}`));
            }
            block.setPermutation(BlockPermutation.resolve(block.type.id, {
                'va:fermented_items': Math.max(fermentedAmount - 1, 0),
                'va:ready_to_collect': fermentedAmount > 1,
                'va:ferment': fermentedType,
            }));
            block.dimension.playSound('bucket.fill_water', block.center());
            player.sendMessage(`Amount Remaining: ${fermentedAmount - 1}`);
            if (fermentedAmount === 1) {
                block.setPermutation(BlockPermutation.resolve(block.type.id));
            }
        }
        else {
            player.sendMessage('Use a Beer Glass to collect beverage');
        }
    }
}
export function fermentationBarrelFermenting(block) {
    const permutation = block.permutation;
    const isFermenting = permutation.getState('va:fermenting');
    const isReady = permutation.getState('va:ready_to_collect');
    const fermentType = permutation.getState('va:ferment');
    const fermentDay = permutation.getState('va:ferment_day');
    const blockLocation = block.center();
    if (isFermenting) {
        const newFermentDay = Math.min(fermentDay + 1, 3);
        block.setPermutation(BlockPermutation.resolve(block.type.id, {
            'va:ferment_day': newFermentDay,
            'va:fermenting': true,
            'va:ferment': fermentType,
        }));
        if (newFermentDay === 3) {
            block.dimension.playSound('block.barrel.open', block.center());
            block.setPermutation(BlockPermutation.resolve(block.type.id, {
                'va:fermenting': false,
                'va:ready_to_collect': true,
                'va:fermented_items': 15,
                'va:ferment': fermentType,
            }));
        }
    }
    else if (isReady) {
        block.dimension.spawnParticle('minecraft:crop_growth_emitter', {
            x: blockLocation.x,
            y: blockLocation.y + 0.5,
            z: blockLocation.z,
        });
    }
}
