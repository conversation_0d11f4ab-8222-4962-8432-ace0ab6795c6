{"format_version": "1.21.0", "minecraft:entity": {"description": {"identifier": "minecraft:cow", "spawn_category": "creature", "is_spawnable": true, "is_summonable": true}, "component_groups": {"minecraft:cow_baby": {"minecraft:is_baby": {}, "minecraft:scale": {"value": 0.5}, "minecraft:ageable": {"duration": 1200, "feed_items": "wheat", "grow_up": {"event": "minecraft:ageable_grow_up", "target": "self"}}, "minecraft:behavior.follow_parent": {"priority": 6, "speed_multiplier": 1.1}}, "minecraft:cow_adult": {"minecraft:experience_reward": {"on_bred": "<PERSON><PERSON>(1,7)", "on_death": "query.last_hit_by_player ? Math.Random(1,3) : 0"}, "minecraft:loot": {"table": "loot_tables/entities/cow.json"}, "minecraft:behavior.breed": {"priority": 3, "speed_multiplier": 1.0}, "minecraft:breedable": {"require_tame": false, "breed_items": "wheat", "breeds_with": {"mate_type": "minecraft:cow", "baby_type": "minecraft:cow", "breed_event": {"event": "minecraft:entity_born", "target": "baby"}}}, "minecraft:interact": {"interactions": [{"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_equipment", "domain": "hand", "subject": "other", "value": "bucket:0"}]}}, "use_item": true, "transform_to_item": "bucket:1", "play_sounds": "milk", "interact_text": "action.interact.milk"}, {"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_equipment", "domain": "hand", "subject": "other", "value": "va:clay_bucket"}]}}, "use_item": true, "transform_to_item": "va:clay_milk_bucket", "play_sounds": "milk", "interact_text": "action.interact.milk"}]}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:type_family": {"family": ["cow", "mob"]}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:navigation.walk": {"can_path_over_water": true, "avoid_water": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:collision_box": {"width": 0.9, "height": 1.3}, "minecraft:nameable": {}, "minecraft:health": {"value": 10, "max": 10}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:movement": {"value": 0.25}, "minecraft:despawn": {"despawn_from_distance": {}}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.panic": {"priority": 1, "speed_multiplier": 1.25}, "minecraft:behavior.mount_pathing": {"priority": 2, "speed_multiplier": 1.5, "target_dist": 0.0, "track_target": true}, "minecraft:behavior.breed": {"priority": 3, "speed_multiplier": 1.0}, "minecraft:behavior.tempt": {"priority": 4, "speed_multiplier": 1.25, "items": ["wheat"]}, "minecraft:behavior.follow_parent": {"priority": 5, "speed_multiplier": 1.1}, "minecraft:behavior.random_stroll": {"priority": 6, "speed_multiplier": 0.8}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 6.0, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 9}, "minecraft:leashable": {"soft_distance": 4.0, "hard_distance": 6.0, "max_distance": 10.0}, "minecraft:balloonable": {}, "minecraft:rideable": {"seat_count": 1, "family_types": ["zombie"], "seats": {"position": [0.0, 1.105, 0.0]}}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {}}, "events": {"minecraft:entity_spawned": {"randomize": [{"weight": 95, "trigger": "minecraft:spawn_adult"}, {"weight": 5, "add": {"component_groups": ["minecraft:cow_baby"]}}]}, "minecraft:entity_born": {"add": {"component_groups": ["minecraft:cow_baby"]}}, "minecraft:entity_transformed": {"remove": {}, "add": {"component_groups": ["minecraft:cow_adult"]}}, "minecraft:ageable_grow_up": {"remove": {"component_groups": ["minecraft:cow_baby"]}, "add": {"component_groups": ["minecraft:cow_adult"]}}, "minecraft:spawn_adult": {"add": {"component_groups": ["minecraft:cow_adult"]}}}}}