{"format_version": "1.10.0", "particle_effect": {"description": {"identifier": "va:beer_glass_break", "basic_render_parameters": {"material": "particles_alpha", "texture": "textures/items/beer_glass/empty"}}, "components": {"minecraft:emitter_rate_instant": {"num_particles": "math.random_integer(16, 32)"}, "minecraft:emitter_lifetime_once": {"active_time": 1}, "minecraft:emitter_shape_point": {"direction": ["Math.random(-0.45, 0.45)", 0.95, "Math.random(-0.45, 0.45)"]}, "minecraft:particle_lifetime_expression": {"max_lifetime": "Math.random(8, 12)"}, "minecraft:particle_initial_spin": {"rotation": "math.random_integer(-90, 90)"}, "minecraft:particle_initial_speed": "Math.random(1.5, 15)", "minecraft:particle_motion_dynamic": {"linear_acceleration": [0, -30, 0], "linear_drag_coefficient": "1.96 * Math.pow(0.52, variable.particle_age)", "rotation_drag_coefficient": 7}, "minecraft:particle_appearance_billboard": {"size": [0.1, 0.1], "facing_camera_mode": "lookat_xyz", "uv": {"texture_width": 16, "texture_height": 16, "uv": [7, 6], "uv_size": [2, 2]}}, "minecraft:particle_motion_collision": {"enabled": "v.particle_age > 0.1", "collision_drag": 8, "coefficient_of_restitution": 0.2, "collision_radius": 0.005}, "minecraft:particle_appearance_lighting": {}}}}