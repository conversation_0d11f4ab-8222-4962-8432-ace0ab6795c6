import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GameMode, Player, system, Vector3 } from "@minecraft/server";
import { getTarget } from "../../../../general_mechanics/targetUtils";
import { getDistance } from "../../../../../utilities/vector3";

/**
 * When the attack should apply in the animation (0.25 seconds = 10 ticks)
 */
const ATTACK_TIMING = 10; // Apply damage at tick 10

/**
 * The total animation time in ticks (0.75 seconds = 15 ticks)
 */
const ANIMATION_TIME = 15; // Total animation time in ticks

/**
 * Cooldown before executing the next attack
 */
const COOLDOWN_TIME = 2;

/**
 * Normalizes a 2D direction vector (x and z only)
 *
 * @param direction Vector to normalize
 * @returns Normalized 2D vector
 */
function normalizeDirection(x: number, z: number): { x: number; z: number } {
  const length = Math.sqrt(x * x + z * z);
  if (length > 0) {
    return { x: x / length, z: z / length };
  }
  return { x: 0, z: 0 };
}

/**
 * Executes the horizontal attack for the Piglin Brute
 * Applies damage and horizontal knockback to nearby entities
 *
 * @param piglinBrute The piglin brute entity
 */
export function executeHorizontalAttack(piglinBrute: Entity): void {
  // Apply damage to nearby entities
  const damageRadius = 3;
  // Use the brute's attack damage (6)
  const damage = 8;

  // Wait for the attack timing before executing the attack
  let timing = system.runTimeout(() => {
    try {
      const isDead = piglinBrute.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(timing);
        return;
      }

      // Find the target to determine direction
      const target = getTarget(piglinBrute, piglinBrute.location, 32, ["piglin_champion", "piglin", "rock"]);
      let dirX = 0;
      let dirZ = 0;

      if (target) {
        // Calculate direction towards target
        const dx = target.location.x - piglinBrute.location.x;
        const dz = target.location.z - piglinBrute.location.z;
        const normalized = normalizeDirection(dx, dz);
        dirX = normalized.x;
        dirZ = normalized.z;
      } else {
        // Use the brute's view direction if no target
        const viewDir = piglinBrute.getViewDirection();
        const normalized = normalizeDirection(viewDir.x, viewDir.z);
        dirX = normalized.x;
        dirZ = normalized.z;
      }

      // Use head location as the initial point and calculate position 1.5 blocks in front
      const headLoc = piglinBrute.getHeadLocation();
      const originPos: Vector3 = {
        x: headLoc.x + dirX * 1.5,
        y: headLoc.y - 0.3,
        z: headLoc.z + dirZ * 1.5
      };

      // Play attack impact sound and particles
      piglinBrute.dimension.spawnParticle("minecraft:critical_hit_emitter", originPos);
      piglinBrute.dimension.playSound("mob.piglin.angry", originPos, { volume: 1.8 });

      // Find entities within the damage radius
      piglinBrute.dimension
        .getEntities({
          location: originPos,
          maxDistance: damageRadius,
          excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
          excludeFamilies: ["piglin_champion", "piglin", "rock"]
        })
        .forEach((entity: Entity) => {
          // Apply damage
          entity.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: piglinBrute });

          // Calculate distance for knockback
          const point1: Vector3 = { x: entity.location.x, y: 0, z: entity.location.z };
          const point2: Vector3 = { x: originPos.x, y: 0, z: originPos.z };
          const distance = getDistance(point1, point2);

          if (distance > 0) {
            // Horizontal attack parameters - strong horizontal knockback
            const horizontalStrength = 1.0; // Strong horizontal knockback
            const verticalStrength = 0.3; // Minimal upward knockback

            try {
              // Try to apply knockback first
              if (entity instanceof Player) {
                const gameMode = entity.getGameMode();
                if (gameMode === GameMode.survival || gameMode === GameMode.adventure) {
                  entity.applyKnockback(dirX, dirZ, horizontalStrength, verticalStrength);
                }
              } else {
                entity.applyKnockback(dirX, dirZ, horizontalStrength, verticalStrength);
              }
            } catch (e) {
              // Fallback to applyImpulse if applyKnockback fails
              const impulse: Vector3 = {
                x: dirX * horizontalStrength,
                y: verticalStrength,
                z: dirZ * horizontalStrength
              };

              entity.applyImpulse(impulse);
            }
          }
        });

      system.clearRun(timing);
    } catch (error) {
      // handle error silently
      system.clearRun(timing);
    }
  }, ATTACK_TIMING);

  // Reset attack property after animation completes
  let resetAttack = system.runTimeout(() => {
    try {
      const isDead = piglinBrute.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(resetAttack);
        return;
      }
      piglinBrute.triggerEvent("ptd_bb:reset_attack");
      system.clearRun(resetAttack);
    } catch (error) {
      // handle error silently
      system.clearRun(resetAttack);
    }
  }, ANIMATION_TIME);

  // Wait for cooldown, then set cooldown property to false to select the next attack
  let cooldown = system.runTimeout(() => {
    try {
      const isDead = piglinBrute.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(cooldown);
        return;
      }
      piglinBrute.setProperty("ptd_bb:cooling_down", false);
      system.clearRun(cooldown);
    } catch (error) {
      // handle error silently
      system.clearRun(cooldown);
    }
  }, ANIMATION_TIME + COOLDOWN_TIME);
  return;
}
