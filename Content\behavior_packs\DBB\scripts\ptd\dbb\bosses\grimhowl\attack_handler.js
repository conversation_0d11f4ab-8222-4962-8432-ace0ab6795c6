import { getTarget, filterValidTargets } from "../general_mechanics/targetUtils";
import { getEntityWeights, selectEvent, updateWeights } from "./attack_controller";
import { doGrimhowlLeftClaw, doGrimhowlRightClaw, doGrimhowlSpinningSlash, doGrimhowlBackstep } from "./attacks/index";
/**
 * Handles melee range attacks for the Grimhowl boss
 * Selects and executes appropriate attacks based on target positions
 *
 * @param sourceEntity - The Grimhowl boss entity
 */
export function grimhowlMeleeRangeAttackHandler(sourceEntity) {
    try {
        if (!sourceEntity)
            return;
        const nearbyEntity = getTarget(sourceEntity, sourceEntity.location, 32, ["ptd_bb_grimhowl"]);
        const frontEntity = getTarget(sourceEntity, sourceEntity.location, 32, ["ptd_bb_grimhowl"], 0.75);
        if (nearbyEntity) {
            const entitiesInAOE = sourceEntity.dimension.getEntities({
                location: sourceEntity.location,
                excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
                maxDistance: 7
            }).filter(filterValidTargets(sourceEntity));
            const entityId = sourceEntity.id;
            const weights = getEntityWeights(entityId);
            let selectedEvent = selectEvent(weights);
            if ((selectedEvent === "clawLeft" || selectedEvent === "clawRight") && !frontEntity) {
                selectedEvent = "spinningSlash";
            }
            if ((selectedEvent === "clawLeft" || selectedEvent === "clawRight") && frontEntity) {
                const dx = frontEntity.location.x - sourceEntity.location.x;
                const dz = frontEntity.location.z - sourceEntity.location.z;
                const yaw = Math.atan2(dz, dx) * 180 / Math.PI - 90; // Minecraft yaw is 0 = south, -90 = east
                sourceEntity.setRotation({ x: 0, y: yaw });
                sourceEntity.teleport(sourceEntity.location, { facingLocation: frontEntity.getHeadLocation(), keepVelocity: false });
                if (selectedEvent === "clawLeft") {
                    doGrimhowlLeftClaw(sourceEntity, entitiesInAOE);
                }
                else {
                    doGrimhowlRightClaw(sourceEntity, entitiesInAOE);
                }
            }
            else if (selectedEvent === "spinningSlash") {
                doGrimhowlSpinningSlash(sourceEntity, entitiesInAOE);
            }
            else if (selectedEvent === "backstep") {
                if (!nearbyEntity) {
                    sourceEntity.triggerEvent('ptd_bb:attack_done');
                    return; // Finish attack if no target and return early
                }
                doGrimhowlBackstep(sourceEntity, nearbyEntity);
            }
            updateWeights(entityId, selectedEvent);
        }
        else {
            sourceEntity.triggerEvent('ptd_bb:attack_done');
            return;
        }
    }
    catch (error) {
        sourceEntity?.triggerEvent('ptd_bb:attack_done');
    }
}
