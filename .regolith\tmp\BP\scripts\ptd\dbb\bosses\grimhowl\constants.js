/**
 * Constants and shared types for Grimhowl boss logic.
 * This file defines constants and type definitions used throughout the Grimhowl boss implementation.
 */
/**
 * Entity types that Grimhowl will target
 */
export const grimHowlTargetsToCheck = ["player", "iron_golem"];
/**
 * Default weights for different attack types
 * Higher values increase the likelihood of selecting that attack
 * Note: backstep has a much higher weight by default
 */
export const defaultEventWeights = {
    clawLeft: 1,
    clawRight: 1,
    spinningSlash: 1,
    backstep: 10
};
