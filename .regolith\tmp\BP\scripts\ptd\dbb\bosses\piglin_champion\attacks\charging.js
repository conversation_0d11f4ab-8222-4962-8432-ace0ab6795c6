import { Entity<PERSON>ama<PERSON><PERSON><PERSON><PERSON>, GameMode, Player, system } from "@minecraft/server";
import { getDistance } from "../../../utilities/vector3";
import { PIGLIN_CHAMPION_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
/**
 * Attack timing constants for charging attack phases
 */
const SPEED_EFFECT_TIMING = 30; // Apply speed 3 effect at tick 30
const CONTINUOUS_DAMAGE_START = 46; // Start continuous damage at tick 46
const SLOWNESS_EFFECT_TIMING = 90; // Apply slowness effect at tick 90
const FINAL_IMPACT_TIMING = 96; // Final impact damage at tick 96
/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 315;
/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;
/**
 * Executes the charging attack for the Piglin Champion using the new timing system
 * Uses localized runTimeout for all attack phases, reset, and cooldown
 *
 * @param piglinChampion The piglin champion entity
 */
export function executeChargingAttack(piglinChampion) {
    // Phase 1: Apply speed 3 effect and trigger charging2 event at tick 30
    let speedTiming = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(speedTiming);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "charging") {
                // Apply speed 3 effect for the duration of charge_2 and charge_3 (60 ticks)
                piglinChampion.addEffect("minecraft:speed", 60, { amplifier: 3, showParticles: false });
                piglinChampion.triggerEvent("ptd_dbb:charging2"); // Trigger animation transition to charging_2
            }
        }
        catch (error) {
            system.clearRun(speedTiming);
        }
    }, SPEED_EFFECT_TIMING);
    // Phase 2: Start continuous damage at tick 46
    let continuousTiming = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(continuousTiming);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "charging") {
                startContinuousChargingDamage(piglinChampion);
            }
        }
        catch (error) {
            system.clearRun(continuousTiming);
        }
    }, CONTINUOUS_DAMAGE_START);
    // Phase 3: Apply slowness effect and trigger stun after charge at tick 90
    let slownessTiming = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(slownessTiming);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "charging") {
                // Apply slowness for the duration of stunned_sitting and charge_4 (220 ticks)
                piglinChampion.addEffect("minecraft:slowness", 220, { amplifier: 250, showParticles: false });
                piglinChampion.triggerEvent("ptd_dbb:stun_after_charge"); // Stun the piglin - remove the melee component group temporarily
            }
        }
        catch (error) {
            system.clearRun(slownessTiming);
        }
    }, SLOWNESS_EFFECT_TIMING);
    // Phase 4: Final impact damage at tick 96
    let finalImpactTiming = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(finalImpactTiming);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "charging") {
                performFinalChargingImpact(piglinChampion);
            }
        }
        catch (error) {
            system.clearRun(finalImpactTiming);
        }
    }, FINAL_IMPACT_TIMING);
    // Reset the attack state to "none" after the animation is complete
    let reset = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            if (isDead) {
                system.clearRun(reset);
                return;
            }
            // Only reset if the attack is still "charging" - prevents interference with stuns
            if (currentAttack === "charging") {
                // Recover from stun after charging attack - add the melee component group back
                piglinChampion.triggerEvent("ptd_dbb:recover_after_charge");
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
            system.clearRun(reset);
        }
        catch (error) {
            system.clearRun(reset);
        }
    }, ANIMATION_TIME);
    // Wait for cooldown, then set cooldown property to false to select the next attack
    let cooldown = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldown);
                return;
            }
            piglinChampion.setProperty("ptd_dbb:cooling_down", false);
            system.clearRun(cooldown);
        }
        catch (error) {
            system.clearRun(cooldown);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
    return;
}
/**
 * Starts continuous damage application during the charging movement
 * Applies damage to entities as the piglin charges through them from tick 46 to 90
 *
 * @param piglinChampion The piglin champion entity
 */
function startContinuousChargingDamage(piglinChampion) {
    // Duration for continuous damage (from tick 46 to 90)
    const continuousDuration = SLOWNESS_EFFECT_TIMING - CONTINUOUS_DAMAGE_START; // 44 ticks
    let ticksElapsed = 0;
    // Apply damage every 4 ticks during the charging movement
    const damageInterval = system.runInterval(() => {
        try {
            // Check if the piglin is still charging
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            // Stop if attack changed, entity died, or we've reached the duration
            if (currentAttack !== "charging" || isDead || ticksElapsed >= continuousDuration) {
                system.clearRun(damageInterval);
                return;
            }
            // Apply damage to entities during the charge (without knockback for continuous damage)
            applyChargingDamage(piglinChampion, false); // false = no knockback during continuous damage
            ticksElapsed += 4; // Increment by interval duration
        }
        catch (e) {
            // Entity might have been removed
            system.clearRun(damageInterval);
        }
    }, 4); // Apply damage every 4 ticks for balanced continuous damage
}
/**
 * Performs the final charging attack impact for the Piglin Champion
 * Applies damage and knockback to nearby entities when the piglin charges into them
 * This is the final impact at tick 96
 *
 * @param piglinChampion The piglin champion entity
 */
function performFinalChargingImpact(piglinChampion) {
    // Apply final impact damage with knockback
    applyChargingDamage(piglinChampion, true); // true = apply knockback for final impact
}
/**
 * Applies charging damage to nearby entities
 *
 * @param piglinChampion The piglin champion entity
 * @param applyKnockback Whether to apply knockback (true for final impact, false for continuous damage)
 */
function applyChargingDamage(piglinChampion, applyKnockback) {
    // Apply damage to nearby entities
    const damageRadius = 4;
    // Use direct damage value instead of percentage
    const damage = PIGLIN_CHAMPION_ATTACK_DAMAGES.charging.damage;
    // Get direction vector directly from the piglin's view direction
    const viewDirection = piglinChampion.getViewDirection();
    const dirX = viewDirection.x;
    const dirZ = viewDirection.z;
    // Calculate position 3 blocks in front of the piglin
    const originPos = {
        x: piglinChampion.location.x + dirX * 3,
        y: piglinChampion.location.y,
        z: piglinChampion.location.z + dirZ * 3
    };
    // Apply damage to nearby entities
    piglinChampion.dimension
        .getEntities({
        location: originPos,
        maxDistance: damageRadius,
        excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
        excludeFamilies: ["piglin_champion", "piglin", "rock"]
    })
        .forEach((entity) => {
        // Only apply knockback if requested (for final impact)
        if (applyKnockback) {
            // Use piglin's direction for knockback
            // Create 2D points (same y-coordinate) to calculate horizontal distance
            const point1 = { x: entity.location.x, y: 0, z: entity.location.z };
            const point2 = { x: originPos.x, y: 0, z: originPos.z };
            const distance = getDistance(point1, point2);
            if (distance > 0) {
                // Use the piglin's direction for knockback
                const nx = dirX;
                const nz = dirZ;
                // Charging attack parameters
                const horizontalStrength = 6.0; // Knock players back 6 blocks
                const verticalStrength = 0.8;
                try {
                    // Try to apply knockback first
                    if (entity instanceof Player) {
                        const gameMode = entity.getGameMode();
                        if (gameMode === GameMode.survival || gameMode === GameMode.adventure) {
                            entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                        }
                    }
                    else {
                        entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                    }
                }
                catch (e) {
                    // Fallback to applyImpulse if applyKnockback fails
                    const impulse = {
                        x: nx * horizontalStrength,
                        y: verticalStrength,
                        z: nz * horizontalStrength
                    };
                    entity.applyImpulse(impulse);
                }
            }
        }
        // Apply damage after knockback/impulse
        entity.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: piglinChampion });
    });
}
