/**
 * Grimhowl boss pounce attack implementation.
 * This file contains the logic for executing the pounce attack,
 * including animation, sound effects, and damage application.
 */
import { system, EntityDamageCause } from "@minecraft/server";
import { filterValidTargets } from "../../general_mechanics/targetUtils";
import { grimHowlTargetsToCheck } from "../constants";
import { GRIMHOWL_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
import { getDirection } from "../../../utilities/vector3";
/**
 * Executes the pounce attack for the Grimhowl boss
 * The attack looks for a target, plays an animation,
 * moves the boss towards the target, and applies damage in an area.
 *
 * @param sourceEntity - The Grimhowl boss entity
 */
export function doGrimhowlPounce(sourceEntity) {
    try {
        if (!sourceEntity)
            return;
        const isSwordMode = sourceEntity.getProperty('ptd_dbb:sword_mode');
        const startLocation = sourceEntity.location;
        const nearbyTargets = grimHowlTargetsToCheck.flatMap((family) => sourceEntity.dimension.getEntities({
            location: startLocation,
            families: [family],
            maxDistance: 32
        }).filter(filterValidTargets(sourceEntity)));
        if (nearbyTargets.length > 0) {
            sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.grunt @a ~ ~ ~ 5${1 + (Math.random() * 0.3)}`);
            const nearestTarget = nearbyTargets[nearbyTargets.length - 1];
            const endLocation = nearestTarget.location;
            const duration = 1; // Duration in seconds
            const steps = Math.ceil(duration * 20); // Number of steps (20 ticks per second)
            const stepX = (endLocation.x - startLocation.x) / steps;
            const stepY = (endLocation.y - startLocation.y) / steps;
            const stepZ = (endLocation.z - startLocation.z) / steps;
            for (let i = 1; i <= steps; i++) {
                system.runTimeout(() => {
                    // Calculate bounce height using a parabolic curve
                    const t = i / steps; // Normalized time (0 to 1)
                    const bounceHeight = 6 * (1 - 4 * (t - 0.5) ** 2); // Parabolic curve peaking at t = 0.5
                    sourceEntity.teleport({
                        x: startLocation.x + stepX * i,
                        y: startLocation.y + stepY * i + bounceHeight,
                        z: startLocation.z + stepZ * i
                    }, { facingLocation: nearestTarget.location });
                }, i * 1); // 1 tick per step
            }
            system.runTimeout(() => {
                if (!sourceEntity)
                    return;
                if (isSwordMode) {
                    sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.pounce_attack @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
                }
                else {
                    sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.pounce_attack_swordless @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
                    sourceEntity.runCommand(`playsound mob.ravager.bite @a ~ ~ ~ 10 ${2 + (Math.random() * 0.3)}`);
                }
                const pouncedTargets = sourceEntity.dimension.getEntities({
                    location: sourceEntity.location,
                    excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
                    maxDistance: 6
                }).filter(filterValidTargets(sourceEntity));
                pouncedTargets.forEach((entity) => {
                    const direction = getDirection(startLocation, entity.location);
                    // Normalize the direction vector
                    const magnitude = Math.sqrt(direction.x ** 2 + direction.y ** 2 + direction.z ** 2);
                    const normalizedDirection = magnitude === 0
                        ? { x: 0, y: 0, z: 0 }
                        : {
                            x: direction.x / magnitude,
                            y: direction.y / magnitude,
                            z: direction.z / magnitude
                        };
                    // @ts-ignore: isSneaking may not be typed in Minecraft API // A.N. It is typed in the API
                    const isSneaking = entity.isSneaking;
                    entity.applyDamage(GRIMHOWL_ATTACK_DAMAGES.backstep_sword.damage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
                    if (!isSneaking) {
                        entity.applyKnockback(normalizedDirection.x, normalizedDirection.z, isSwordMode ? 0 : 2.5, 0.5);
                    }
                });
            }, duration * 20); // Apply damage after the full duration
            system.runTimeout(() => {
                if (!sourceEntity)
                    return;
                sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.pre_pounce @a ~ ~ ~ 32 ${1 + (Math.random() * 0.3)}`);
            }, 0.25 * 20);
        }
        else {
            sourceEntity.triggerEvent('ptd_dbb:attack_done');
            return;
        }
        sourceEntity.triggerEvent('ptd_dbb:grimhowl_pounce');
    }
    catch (error) {
        sourceEntity?.triggerEvent('ptd_dbb:attack_done');
    }
}
