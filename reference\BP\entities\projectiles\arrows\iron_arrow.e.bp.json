{"format_version": "1.21.30", "minecraft:entity": {"description": {"identifier": "va:iron_arrow", "is_spawnable": false, "is_summonable": true, "is_experimental": false, "runtime_identifier": "minecraft:snowball", "properties": {"va:dropped": {"type": "bool", "client_sync": false, "default": false}}}, "component_groups": {"va:despawn": {"minecraft:instant_despawn": {"remove_child_entities": false}}}, "events": {"va:despawn": {"add": {"component_groups": ["va:despawn"]}}, "va:drop": {"set_property": {"va:dropped": true}}}, "components": {"minecraft:collision_box": {"width": 0.25, "height": 0.25}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:timer": {"time": 60, "looping": false, "time_down_event": {"event": "va:despawn", "target": "self"}}, "minecraft:entity_sensor": {"relative_range": true, "subsensors": [{"event_filters": {"all_of": [{"test": "bool_property", "subject": "self", "domain": "va:dropped", "value": false}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "on_ground", "subject": "self", "value": true}]}, "event": "va:drop", "minimum_count": 1, "range": [0.7, 0.1]}]}, "minecraft:projectile": {"on_hit": {"impact_damage": {"damage": 2, "knockback": true, "semi_random_diff_damage": true, "destroy_on_hit": true, "max_critical_damage": 16, "min_critical_damage": 12, "power_multiplier": 0.98}, "arrow_effect": {"apply_effect_to_blocking_targets": false}, "stick_in_ground": {"shake_time": 0.35}}, "hit_sound": "bow.hit", "power": 5.0, "gravity": 0.05, "uncertainty_base": 1, "uncertainty_multiplier": 0, "anchor": 1, "should_bounce": true, "offset": [0, -0.1, 0]}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {"default_values": {"max_optimized_distance": 80.0, "max_dropped_ticks": 7, "use_motion_prediction_hints": true}}}}}