{
  "format_version": "1.21.70",
  "minecraft:entity": {
    "description": {
      "identifier": "ptd_bb:piglin_champion",
      "is_spawnable": true,
      "is_summonable": true,
      "properties": {
        "ptd_bb:spawning": {
          "type": "bool",
          "client_sync": true,
          "default": true
        },
        "ptd_bb:dead": {
          "type": "bool",
          "client_sync": true,
          "default": false
        },

        "ptd_bb:attack": {
          "type": "enum",
          "client_sync": true,
          "default": "none",
          "values": [
            "none",
            "horizontal",
            "vertical",
            "foot_stomp",
            "spin_slam",
            "body_slam",
            "upchuck",
            "charging",
            "healing",
            "summoning_chant",
            "stunned_standing",
            "stunned_sitting"
          ]
        },
        "ptd_bb:cooling_down": {
          "type": "bool",
          "client_sync": true,
          "default": false
        },
        "ptd_bb:attack_started": {
          "type": "bool",
          "client_sync": false,
          "default": false
        },
        "ptd_bb:last_heal_threshold": {
          "type": "int",
          "client_sync": false,
          "range": [0, 3],
          "default": 3
        },
        "ptd_bb:stun_standing_triggered": {
          "type": "bool",
          "client_sync": true,
          "default": false
        },
        "ptd_bb:stun_sitting_triggered": {
          "type": "bool",
          "client_sync": true,
          "default": false
        }
      },
      "animations": {
        "walk": "animation.ptd_bb.piglin_champion.walk",
        "spawn": "animation.ptd_bb.piglin_champion.spawn",
        "general": "controller.animation.ptd_bb.piglin_champion.general"
      },
      "scripts": {
        "animate": [
          "general",
          {
            "spawn": "q.property('ptd_bb:spawning') == true"
          }
        ]
      }
    },
    "component_groups": {
      "ptd_bb:spawning": {
        "minecraft:is_collidable": {},
        "minecraft:timer": {
          "time": 5.6667,
          "looping": false,
          "time_down_event": {
            "event": "ptd_bb:on_spawn",
            "target": "self"
          }
        },
        "minecraft:damage_sensor": {
          "triggers": [
            {
              "cause": "all",
              "deals_damage": "no"
            }
          ]
        }
      },
      "ptd_bb:default": {
        "minecraft:damage_sensor": {
          "triggers": [
            {
              "on_damage": {
                "filters": {
                  "test": "actor_health",
                  "subject": "self",
                  "operator": "<=",
                  "value": 10
                },
                "event": "ptd_bb:dead",
                "target": "self"
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "bool_property",
                  "domain": "ptd_bb:dead",
                  "subject": "self",
                  "value": true
                }
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "enum_property",
                  "domain": "ptd_bb:attack",
                  "subject": "self",
                  "value": "healing"
                }
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "is_family",
                  "subject": "other",
                  "value": "piglin_champion"
                }
              },
              "deals_damage": "no"
            }
          ]
        },
        "minecraft:movement.basic": {},
        "minecraft:behavior.float": {
          "priority": 0
        },
        "minecraft:navigation.walk": {
          "avoid_damage_blocks": true,
          "can_pass_doors": true,
          "can_jump": true,
          "is_amphibious": true,
          "can_float": true
        }
      },
      "ptd_bb:targeting": {
        "minecraft:environment_sensor": {
          "triggers": [
            {
              "filters": [
                {
                  "test": "target_distance",
                  "subject": "other",
                  "operator": "<=",
                  "value": 32
                },
                {
                  "test": "enum_property",
                  "subject": "self",
                  "domain": "ptd_bb:attack",
                  "operator": "==",
                  "value": "none"
                },
                {
                  "test": "bool_property",
                  "subject": "self",
                  "domain": "ptd_bb:cooling_down",
                  "operator": "==",
                  "value": false
                }
              ],
              "event": "ptd_bb:attack"
            }
          ]
        },
        "minecraft:behavior.nearest_prioritized_attackable_target": {
          "priority": 0,
          "must_see": true,
          "attack_interval": 1,
          "reselect_targets": true,
          "must_see_forget_duration": 0,
          "reevaluate_description": true,
          "entity_types": [
            {
              "priority": 0,
              "max_dist": 64,
              "filters": {
                "test": "is_family",
                "subject": "other",
                "value": "player"
              }
            },
            {
              "priority": 1,
              "max_dist": 64,
              "filters": {
                "all_of": [
                  {
                    "test": "is_family",
                    "subject": "other",
                    "value": "boss"
                  },
                  {
                    "test": "is_family",
                    "subject": "other",
                    "operator": "!=",
                    "value": "piglin_champion"
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_bb:spawning",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_bb:dead",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  }
                ]
              }
            },
            {
              "priority": 2,
              "max_dist": 64,
              "filters": {
                "all_of": [
                  {
                    "test": "is_family",
                    "subject": "other",
                    "value": "minion"
                  },
                  {
                    "test": "is_family",
                    "subject": "other",
                    "operator": "!=",
                    "value": "piglin_champion"
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_bb:spawning",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_bb:dead",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  }
                ]
              }
            }
          ]
        }
      },
      "ptd_bb:melee": {
        "minecraft:attack": {
          "damage": 0
        },
        "minecraft:movement": {
          "max": 0.15
        },
        "minecraft:behavior.random_look_around": {
          "priority": 5
        },
        "minecraft:behavior.random_stroll": {
          "priority": 4
        }
      },
      "ptd_bb:charging2": {
        "minecraft:movement": {
          "max": 0.5
        }
      },
      "ptd_bb:collidable": {
        "minecraft:is_collidable": {}
      },
      "ptd_bb:dead": {
        "minecraft:timer": {
          "time": 20,
          "looping": false,
          "time_down_event": {
            "event": "ptd_bb:despawn",
            "target": "self"
          }
        },
        "minecraft:collision_box": {
          "width": 2.5,
          "height": 2.6
        },
        "minecraft:custom_hit_test": {
          "hitboxes": [
            {
              "width": 1.9,
              "height": 2.4,
              "pivot": [0, 1.2, -1.7]
            }
          ]
        },
        "minecraft:is_collidable": {},
        "minecraft:movement": {
          "max": 0
        },
        "minecraft:navigation.walk": {
          "is_amphibious": false,
          "can_pass_doors": false,
          "can_walk": false,
          "can_swim": false,
          "can_sink": false,
          "avoid_sun": false
        },
        "minecraft:behavior.random_look_around": {
          "priority": 999999
        },
        "minecraft:behavior.random_stroll": {
          "priority": 999999
        },
        "minecraft:body_rotation_blocked": {}
      },
      "ptd_bb:despawn": {
        "minecraft:instant_despawn": {}
      }
    },
    "events": {
      "minecraft:entity_spawned": {
        "add": {
          "component_groups": ["ptd_bb:spawning"]
        }
      },
      "ptd_bb:on_spawn": {
        "sequence": [
          {
            "remove": {
              "component_groups": ["ptd_bb:spawning"]
            },
            "set_property": {
              "ptd_bb:spawning": false
            }
          },
          {
            "add": {
              "component_groups": ["ptd_bb:default", "ptd_bb:targeting", "ptd_bb:melee"]
            }
          }
        ]
      },
      "ptd_bb:despawn": {
        "add": {
          "component_groups": ["ptd_bb:despawn"]
        }
      },
      "ptd_bb:attack": {},
      "ptd_bb:healing_ability": {
        "sequence": [
          {
            "set_property": {
              "ptd_bb:attack": "healing"
            },
            "add": {
              "component_groups": [
                "ptd_bb:collidable",
                "ptd_bb:default" // Re-initialize the damage sensor so it doesn't bypass the death mechanics
              ]
            }
          }
        ]
      },
      "ptd_bb:end_healing": {
        "sequence": [
          {
            "remove": {
              "component_groups": ["ptd_bb:collidable"]
            },
            "add": {
              "component_groups": [
                "ptd_bb:default" // Re-initialize the damage sensor so it doesn't bypass the death mechanics
              ]
            }
          }
        ]
      },
      "ptd_bb:dead": {
        "sequence": [
          {
            "set_property": {
              "ptd_bb:dead": true
            }
          },
          {
            "remove": {
              "component_groups": ["ptd_bb:targeting", "ptd_bb:melee"]
            }
          },
          {
            "add": {
              "component_groups": [
                "ptd_bb:dead",
                "ptd_bb:default" // Re-initialize the damage sensor so it doesn't bypass the death mechanics
              ]
            }
          }
        ]
      },
      "ptd_bb:horizontal_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_bb:attack": "horizontal"
            }
          }
        ]
      },
      "ptd_bb:vertical_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_bb:attack": "vertical"
            }
          }
        ]
      },
      "ptd_bb:foot_stomp_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_bb:attack": "foot_stomp"
            }
          }
        ]
      },
      "ptd_bb:spin_slam_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_bb:attack": "spin_slam"
            }
          }
        ]
      },
      "ptd_bb:body_slam_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_bb:attack": "body_slam"
            }
          }
        ]
      },
      "ptd_bb:upchuck_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_bb:attack": "upchuck"
            },
            "add": {
              "component_groups": ["ptd_bb:collidable", "ptd_bb:default"]
            }
          }
        ]
      },
      "ptd_bb:charging_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_bb:attack": "charging"
            }
          }
        ]
      },
      "ptd_bb:summoning_chant_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_bb:attack": "summoning_chant"
            }
          }
        ]
      },
      "ptd_bb:charging2": {
        "sequence": [
          {
            "add": {
              "component_groups": [
                "ptd_bb:charging2",
                "ptd_bb:default" // Re-initialize the damage sensor so it doesn't bypass the death mechanics
              ]
            }
          }
        ]
      },
      "ptd_bb:on_charging2": {
        "sequence": [
          {
            "remove": {
              "component_groups": ["ptd_bb:charging2"]
            }
          },
          {
            "add": {
              "component_groups": [
                "ptd_bb:melee",
                "ptd_bb:default" // Re-initialize the damage sensor so it doesn't bypass the death mechanics
              ]
            }
          }
        ]
      },
      "ptd_bb:stun_after_charge": {
        "sequence": [
          {
            "remove": {
              "component_groups": ["ptd_bb:melee"]
            }
          },
          {
            "add": {
              "component_groups": [
                "ptd_bb:default" // Re-initialize the damage sensor so it doesn't bypass the death mechanics
              ]
            }
          }
        ]
      },
      "ptd_bb:recover_after_charge": {
        "sequence": [
          {
            "add": {
              "component_groups": [
                "ptd_bb:melee",
                "ptd_bb:default" // Re-initialize the damage sensor so it doesn't bypass the death mechanics
              ]
            }
          }
        ]
      },
      "ptd_bb:reset_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_bb:attack": "none",
              "ptd_bb:cooling_down": true,
              "ptd_bb:attack_started": false
            }
          }
        ]
      },
      "ptd_bb:stunned_standing": {
        "sequence": [
          {
            "set_property": {
              "ptd_bb:attack": "stunned_standing",
              "ptd_bb:stun_standing_triggered": true
            },
            "remove": {
              "component_groups": ["ptd_bb:targeting", "ptd_bb:melee", "ptd_bb:default"]
            },
            "add": {
              "component_groups": ["ptd_bb:collidable"]
            }
          }
        ]
      },
      "ptd_bb:stunned_sitting": {
        "sequence": [
          {
            "set_property": {
              "ptd_bb:attack": "stunned_sitting",
              "ptd_bb:stun_sitting_triggered": true
            },
            "remove": {
              "component_groups": ["ptd_bb:targeting", "ptd_bb:melee", "ptd_bb:default"]
            },
            "add": {
              "component_groups": ["ptd_bb:collidable"]
            }
          }
        ]
      },
      "ptd_bb:remove_targeting": {
        "sequence": [
          {
            "remove": {
              "component_groups": ["ptd_bb:targeting", "ptd_bb:default", "ptd_bb:melee"]
            }
          }
        ]
      },
      "ptd_bb:restore_targeting": {
        "sequence": [
          {
            "add": {
              "component_groups": ["ptd_bb:targeting", "ptd_bb:default", "ptd_bb:melee"]
            }
          }
        ]
      },
      "ptd_bb:on_load": {
        "sequence": [
          {
            "set_property": {
              "ptd_bb:attack": "none",
              "ptd_bb:cooling_down": false
            }
          },
          {
            "add": {
              "component_groups": ["ptd_bb:default", "ptd_bb:targeting", "ptd_bb:melee"]
            }
          }
        ]
      }
    },
    "components": {
      "minecraft:type_family": {
        "family": ["piglin_champion", "boss"]
      },
      "minecraft:collision_box": {
        "width": 2,
        "height": 3.5
      },
      "minecraft:custom_hit_test": {
        "hitboxes": [
          {
            "width": 2.3,
            "height": 4.5,
            "pivot": [0, 2.25, 0]
          }
        ]
      },
      "minecraft:health": {
        "value": 1800,
        "max": 1800
      },
      "minecraft:boss": {
        "hud_range": 64,
        "name": "Piglin Champion",
        "should_darken_sky": false
      },
      "minecraft:damage_sensor": {
        "triggers": [
          {
            "on_damage": {
              "filters": {
                "test": "actor_health",
                "subject": "self",
                "operator": "<=",
                "value": 10
              },
              "event": "ptd_bb:dead",
              "target": "self"
            },
            "deals_damage": "no"
          },
          {
            "on_damage": {
              "filters": {
                "test": "bool_property",
                "domain": "ptd_bb:dead",
                "subject": "self",
                "value": true
              }
            },
            "deals_damage": "no"
          },
          {
            "on_damage": {
              "filters": {
                "test": "enum_property",
                "domain": "ptd_bb:attack",
                "subject": "self",
                "value": "healing"
              }
            },
            "deals_damage": "no"
          },
          {
            "on_damage": {
              "filters": {
                "test": "is_family",
                "subject": "other",
                "value": "piglin_champion"
              }
            },
            "deals_damage": "no"
          }
        ]
      },
      "minecraft:variable_max_auto_step": {
        "base_value": 1.0625,
        "jump_prevented_value": 1.0625
      },
      "minecraft:jump.static": {},
      "minecraft:movement.basic": {},
      "minecraft:navigation.walk": {
        "can_path_over_water": true,
        "can_pass_doors": true,
        "can_break_doors": false,
        "avoid_water": true
      },
      /*
       * This allows the entity to follow/go to the target without attacking
       * Since the attacks are handled in the scripts
       */
      "minecraft:behavior.melee_box_attack": {
        "priority": 2,
        "can_spread_on_fire": true,
        "speed_multiplier": 1,
        "horizontal_reach": 0,
        "cooldown_time": 999999999
      },
      "minecraft:behavior.float": {
        "priority": 0
      },
      "minecraft:knockback_resistance": {
        "value": 0.9
      },
      "minecraft:follow_range": {
        "value": 256,
        "max": 256
      },
      "minecraft:physics": {},
      "minecraft:is_stackable": {},
      "minecraft:floats_in_liquid": {},
      "minecraft:persistent": {},
      "minecraft:conditional_bandwidth_optimization": {}
    }
  }
}
