import { system } from "@minecraft/server";
import { getTarget } from "../../../../general_mechanics/targetUtils";
const SUMMON_TIMING = 90;
const ANIMATION_TIME = 180;
const COOLDOWN_TIME = 20;
const ATTACK_CONFIG = {
    MAX_MINIONS: 4,
    SPAWN_RANGE: 8,
};
export function executeRightSummonAttack(voidHydra) {
    let summonTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(summonTiming);
                return;
            }
            if (voidHydra.getProperty("ptd_dbb:attack") === "right_summon") {
                performSummon(voidHydra);
            }
        }
        catch (error) {
            system.clearRun(summonTiming);
        }
    }, SUMMON_TIMING);
    let resetTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetTiming);
                return;
            }
            if (voidHydra.getProperty("ptd_dbb:attack") === "right_summon") {
                voidHydra.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            system.clearRun(resetTiming);
        }
    }, ANIMATION_TIME);
    let cooldownTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldownTiming);
                return;
            }
            voidHydra.setProperty("ptd_dbb:cooling_down", false);
        }
        catch (error) {
            system.clearRun(cooldownTiming);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
}
function performSummon(voidHydra) {
    try {
        const target = getTarget(voidHydra, voidHydra.location, 32, ["void_hydra"]);
        if (!target)
            return;
        // Count existing minions
        const existingMinions = voidHydra.dimension.getEntities({
            families: ["minions"],
            location: voidHydra.location,
            maxDistance: 64
        });
        const minionsToSpawn = Math.max(0, ATTACK_CONFIG.MAX_MINIONS - existingMinions.length);
        if (minionsToSpawn === 0)
            return;
        const targetPos = target.location;
        for (let i = 0; i < minionsToSpawn; i++) {
            const angle = (Math.PI * 2 * i) / minionsToSpawn;
            const spawnPos = {
                x: targetPos.x + Math.cos(angle) * ATTACK_CONFIG.SPAWN_RANGE,
                y: targetPos.y,
                z: targetPos.z + Math.sin(angle) * ATTACK_CONFIG.SPAWN_RANGE
            };
            const delay = i * 10;
            system.runTimeout(() => {
                try {
                    const isDead = voidHydra.getProperty("ptd_dbb:dead");
                    if (isDead)
                        return;
                    // Spawn warning particles
                    voidHydra.dimension.spawnParticle("minecraft:portal", spawnPos);
                    // Spawn minion after delay
                    system.runTimeout(() => {
                        try {
                            // Spawn void minion (placeholder - would need actual minion entity)
                            voidHydra.dimension.spawnEntity("minecraft:zombie", spawnPos);
                            voidHydra.dimension.spawnParticle("minecraft:large_explosion", spawnPos);
                        }
                        catch (error) {
                            // Handle errors silently
                        }
                    }, 20);
                }
                catch (error) {
                    // Handle errors silently
                }
            }, delay);
        }
    }
    catch (error) {
        // Handle errors silently
    }
}
