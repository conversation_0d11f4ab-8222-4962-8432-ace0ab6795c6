import { system } from '@minecraft/server';
/**
 * Spawns an entity at a specific location in a given dimension.
 * @param entityId - The identifier string for the entity type to spawn
 * @param dimension - The dimension where the entity will be spawned
 * @param location - The coordinates where the entity will be spawned
 */
export function summonEntity(entityId, dimension, location) {
    dimension.spawnEntity(entityId, location);
    return;
}
/**
 * Spawns multiple entities with a specified delay between each spawn
 * Can handle multiple entity types with different quantities
 *
 * @param dimension - The dimension where the entities will be spawned
 * @param entityConfigs - Array of entity configurations with entity IDs and counts
 * @param locationCallback - Function that returns spawn location for each entity
 * @param delay - Delay in ticks between spawns (default: 2)
 * @param onEntitySpawned - Optional callback function when an entity is spawned
 * @returns A promise that resolves when all entities are spawned
 */
export function spawnEntitiesWithInterval(dimension, entityConfigs, locationCallback, delay = 2, onEntitySpawned) {
    return new Promise((resolve) => {
        // Calculate total entities to spawn
        let totalToSpawn = 0;
        const entityCounts = new Map();
        // Initialize counts for each entity type
        entityConfigs.forEach((config) => {
            totalToSpawn += config.count;
            entityCounts.set(config.entityId, {
                current: 0,
                total: config.count
            });
        });
        let totalSpawned = 0;
        // Create an interval to spawn entities with a delay
        const spawnInterval = system.runInterval(() => {
            try {
                if (totalSpawned < totalToSpawn) {
                    // Find the next entity type to spawn based on configured ratios
                    let entityIdToSpawn = null;
                    for (const [entityId, counts] of entityCounts.entries()) {
                        if (counts.current < counts.total) {
                            entityIdToSpawn = entityId;
                            break;
                        }
                    }
                    if (entityIdToSpawn) {
                        // Get a location for the entity
                        const spawnPos = locationCallback();
                        if (spawnPos) {
                            // Spawn the entity
                            const entity = dimension.spawnEntity(entityIdToSpawn, spawnPos);
                            // Call the callback if provided
                            if (onEntitySpawned && entity) {
                                onEntitySpawned(entity, entityIdToSpawn);
                            }
                            // Update counters
                            const counts = entityCounts.get(entityIdToSpawn);
                            if (counts) {
                                counts.current++;
                            }
                            totalSpawned++;
                        }
                    }
                    // Stop the interval when all entities are spawned
                    if (totalSpawned >= totalToSpawn) {
                        system.clearRun(spawnInterval);
                        resolve();
                    }
                }
            }
            catch (error) {
                console.warn(`Failed to spawn entity in interval: ${error}`);
                system.clearRun(spawnInterval);
                resolve();
            }
        }, delay);
    });
}
