import { Entity, EntityDamageCause, Vector3, system } from "@minecraft/server";
import { VOID_HYDRA_ATTACK_DAMAGES } from "../../../../general_attacks/attackDamages";
import { getTarget } from "../../../../general_mechanics/targetUtils";
import { fixedPosRaycast } from "../../../../../utilities/raycasts";

/**
 * Attack timing constants for left railgun attack
 */
const RAILGUN_FIRE_TIMING = 70; // Fire railgun at tick 70
const ANIMATION_TIME = 140; // Total animation time in ticks
const COOLDOWN_TIME = 20; // Cooldown time in ticks after the attack completes

/**
 * Attack configuration
 */
const ATTACK_CONFIG = {
  /** Maximum range of the railgun */
  MAX_RANGE: 32,
  /** Width of the railgun beam */
  BEAM_WIDTH: 1.5,
  /** Number of beam segments for particle effects */
  BEAM_SEGMENTS: 20
};

/**
 * Executes the left railgun attack for the Void Hydra
 * Fires a precise, high-damage beam at the target
 *
 * @param voidHydra The void hydra entity
 */
export function executeLeftRailgunAttack(voidHydra: Entity): void {
  // Fire railgun at tick 70
  let railgunFireTiming = system.runTimeout(() => {
    try {
      const isDead = voidHydra.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(railgunFireTiming);
        return;
      }

      if (voidHydra.getProperty("ptd_bb:attack") === "left_railgun") {
        fireRailgun(voidHydra);
      }
    } catch (error) {
      system.clearRun(railgunFireTiming);
    }
  }, RAILGUN_FIRE_TIMING);

  // Reset attack and start cooldown after animation completes
  let resetTiming = system.runTimeout(() => {
    try {
      const isDead = voidHydra.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(resetTiming);
        return;
      }

      if (voidHydra.getProperty("ptd_bb:attack") === "left_railgun") {
        voidHydra.triggerEvent("ptd_bb:reset_attack");
      }
    } catch (error) {
      system.clearRun(resetTiming);
    }
  }, ANIMATION_TIME);

  // End cooldown after cooldown time
  let cooldownTiming = system.runTimeout(() => {
    try {
      const isDead = voidHydra.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(cooldownTiming);
        return;
      }

      voidHydra.setProperty("ptd_bb:cooling_down", false);
    } catch (error) {
      system.clearRun(cooldownTiming);
    }
  }, ANIMATION_TIME + COOLDOWN_TIME);
}

/**
 * Fires the railgun beam at the target
 * @param voidHydra The void hydra entity
 */
function fireRailgun(voidHydra: Entity): void {
  try {
    const target = getTarget(voidHydra, voidHydra.location, 32, ["void_hydra"]);
    if (!target) return;

    const origin: Vector3 = {
      x: voidHydra.location.x,
      y: voidHydra.location.y + 3, // Fire from head height
      z: voidHydra.location.z
    };
    const targetPos: Vector3 = target.location;
    const damage = VOID_HYDRA_ATTACK_DAMAGES.left_railgun.damage;

    // Calculate direction vector
    const direction = {
      x: targetPos.x - origin.x,
      y: targetPos.y - origin.y,
      z: targetPos.z - origin.z
    };

    // Normalize direction
    const magnitude = Math.sqrt(direction.x ** 2 + direction.y ** 2 + direction.z ** 2);
    if (magnitude === 0) return;

    direction.x /= magnitude;
    direction.y /= magnitude;
    direction.z /= magnitude;

    // Use raycast utility to get beam positions
    const maxDistance = Math.min(magnitude, ATTACK_CONFIG.MAX_RANGE);
    const endPos = {
      x: origin.x + direction.x * maxDistance,
      y: origin.y + direction.y * maxDistance,
      z: origin.z + direction.z * maxDistance
    };

    const beamPositions = fixedPosRaycast(origin, endPos, maxDistance, 0.5);
    const entitiesHit = new Set<Entity>();

    // Process each position along the beam
    beamPositions.forEach((beamPos, index) => {
      // Check for entities at this beam position
      voidHydra.dimension
        .getEntities({
          location: beamPos,
          maxDistance: ATTACK_CONFIG.BEAM_WIDTH,
          excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
          excludeFamilies: ["void_hydra", "boss"]
        })
        .forEach((entity) => {
          if (!entitiesHit.has(entity)) {
            entitiesHit.add(entity);

            // Apply damage
            entity.applyDamage(damage, {
              cause: EntityDamageCause.entityAttack,
              damagingEntity: voidHydra
            });

            // Apply strong knockback in the beam direction
            try {
              entity.applyKnockback(direction.x, direction.z, 1.5, 0.5);
            } catch (knockbackError) {
              try {
                const impulse = {
                  x: direction.x * 0.6,
                  y: 0.3,
                  z: direction.z * 0.6
                };
                entity.applyImpulse(impulse);
              } catch (impulseError) {
                // Ignore if both methods fail
              }
            }
          }
        });

      // Spawn beam particles (reduce density)
      if (index % 4 === 0) {
        voidHydra.dimension.spawnParticle("minecraft:dragon_breath_trail", beamPos);
      }
    });

    // Spawn muzzle flash at origin
    voidHydra.dimension.spawnParticle("minecraft:large_explosion", origin);

    // Spawn impact effect at end of beam
    const impactPos = {
      x: origin.x + direction.x * Math.min(magnitude, ATTACK_CONFIG.MAX_RANGE),
      y: origin.y + direction.y * Math.min(magnitude, ATTACK_CONFIG.MAX_RANGE),
      z: origin.z + direction.z * Math.min(magnitude, ATTACK_CONFIG.MAX_RANGE)
    };
    
    voidHydra.dimension.spawnParticle("minecraft:huge_explosion_emitter", impactPos);

    // Create additional visual effects along the beam
    for (let j = 0; j < 5; j++) {
      const effectDistance = (j / 4) * Math.min(magnitude, ATTACK_CONFIG.MAX_RANGE);
      const effectPos = {
        x: origin.x + direction.x * effectDistance,
        y: origin.y + direction.y * effectDistance,
        z: origin.z + direction.z * effectDistance
      };
      
      system.runTimeout(() => {
        try {
          voidHydra.dimension.spawnParticle("minecraft:critical_hit_emitter", effectPos);
        } catch (error) {
          // Handle errors silently
        }
      }, j * 2); // Stagger the effects
    }
  } catch (error) {
    // Handle errors silently
  }
}
