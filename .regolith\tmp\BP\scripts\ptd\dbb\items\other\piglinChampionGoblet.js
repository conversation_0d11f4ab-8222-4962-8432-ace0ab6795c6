import { EffectTypes, ItemComponentTypes } from "@minecraft/server";
import { damageItemstack } from "../damageItemstack";
/**
 * Piglin Champion Goblet Item Handler
 *
 * This module contains functionality for the Piglin Champion Goblet item,
 * a special item dropped by the Piglin Champion boss. When used, the goblet
 * provides powerful regeneration effects to the player, simulating drinking
 * from the champion's goblet.
 *
 * The item has a cooldown period and will take durability damage with each use.
 */
/**
 * Handles the use action for the Piglin Champion Goblet item
 *
 * When a player uses the goblet, it:
 * - Applies a level 5 regeneration effect (amplifier 4) for 5 seconds (100 ticks)
 * - Starts the item's cooldown (defined in the item JSON, typically 15 seconds)
 * - Damages the item's durability by a random amount between 10-60 points
 * - Plays a potion brewing sound effect slightly in front of the player
 *
 * @param player - The player who used the goblet
 * @param item - The goblet ItemStack being used
 * @returns void
 *
 * @throws Will catch and log a warning if any errors occur during execution
 */
export function piglinChampionGobletOnUse(player, item) {
    try {
        const itemTypeId = item.type.id;
        // Get the cooldown component from the item
        const cooldownComponent = item.getComponent(ItemComponentTypes.Cooldown);
        if (cooldownComponent) {
            // Apply regeneration effect (level 5 for 5 seconds)
            player.addEffect(EffectTypes.get('minecraft:regeneration'), 100, { amplifier: 4, showParticles: false });
            // Start the item's cooldown (defined in the item JSON)
            cooldownComponent.startCooldown(player);
            // Damage the item's durability by a random amount between 10-60 points
            damageItemstack(player, itemTypeId, 10, 60);
            // Calculate position for sound effect (slightly in front of player's head)
            const headLoc = player.getHeadLocation();
            const direction = player.getViewDirection();
            const soundPos = {
                x: headLoc.x + (direction.x * 1),
                y: headLoc.y + (direction.y * 1),
                z: headLoc.z + (direction.z * 1),
            };
            player.playSound('random.potion.brewed', { location: soundPos, volume: 2 });
        }
    }
    catch (error) {
        console.warn(`Error in piglinChampionGobletOnUse: ${error}`);
    }
    return;
}
