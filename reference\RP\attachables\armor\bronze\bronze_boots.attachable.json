{"format_version": "1.20.50", "minecraft:attachable": {"description": {"identifier": "va:bronze_boots", "materials": {"default": "entity_emissive_alpha", "enchanted": "armor_enchanted"}, "textures": {"default": "textures/attachables/armor/bronze/boots", "enchanted": "textures/misc/enchanted_item_glint"}, "geometry": {"default": "geometry.bronze_boots"}, "scripts": {"parent_setup": "variable.boots_layer_visible = 0.0;"}, "render_controllers": ["controller.render.item_default"]}}}