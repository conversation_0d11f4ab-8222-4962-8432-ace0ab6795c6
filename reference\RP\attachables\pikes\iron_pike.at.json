{"format_version": "1.10.0", "minecraft:attachable": {"description": {"identifier": "va:iron_pike", "min_engine_version": "1.8.0", "materials": {"default": "entity_alphatest", "enchanted": "entity_alphatest_glint"}, "textures": {"default": "textures/attachables/pikes/iron_pike", "enchanted": "textures/misc/enchanted_item_glint"}, "geometry": {"default": "geometry.pike"}, "animations": {"first_person_hold": "animation.pike.first_person_hold", "third_person_hold": "animation.pike.third_person_hold"}, "scripts": {"animate": [{"first_person_hold": "c.is_first_person"}, {"third_person_hold": "!c.is_first_person"}]}, "render_controllers": ["controller.render.item_default"]}}}