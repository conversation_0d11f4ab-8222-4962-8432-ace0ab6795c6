{
  "format_version": "1.21.80",
  "minecraft:entity": {
    "description": {
      "identifier": "ptd_dbb:piglin_marauder",
      "is_spawnable": true,
      "is_summonable": true,
      "properties": {
        "ptd_dbb:spawning": {
          "type": "bool",
          "client_sync": true,
          "default": true
        },
        "ptd_dbb:dead": {
          "type": "bool",
          "client_sync": true,
          "default": false
        },
        "ptd_dbb:attack": {
          "type": "enum",
          "client_sync": true,
          "default": "none",
          "values": ["none", "slam", "sweep"]
        },
        "ptd_dbb:cooling_down": {
          "type": "bool",
          "client_sync": true,
          "default": false
        }
      }
    },
    "component_groups": {
      "ptd_dbb:spawning": {
        "minecraft:is_collidable": {},
        "minecraft:timer": {
          "time": 1.125,
          "looping": false,
          "time_down_event": {
            "event": "ptd_dbb:on_spawn",
            "target": "self"
          }
        }
      },
      "ptd_dbb:default": {
        "minecraft:physics": {},
        "minecraft:attack": {
          "damage": 10
        },
        "minecraft:movement.basic": {},
        "minecraft:jump.static": {},
        "minecraft:navigation.walk": {
          "can_pass_doors": true,
          "can_break_doors": true,
          "can_jump": true,
          "can_path_over_water": true,
          "avoid_damage_blocks": true,
          "avoid_water": false
        },
        "minecraft:environment_sensor": {
          "triggers": [
            {
              "filters": [
                {
                  "test": "target_distance",
                  "subject": "other",
                  "operator": "<=",
                  "value": 3.5
                },
                {
                  "test": "enum_property",
                  "subject": "self",
                  "domain": "ptd_dbb:attack",
                  "operator": "==",
                  "value": "none"
                },
                {
                  "test": "bool_property",
                  "subject": "self",
                  "domain": "ptd_dbb:cooling_down",
                  "operator": "==",
                  "value": false
                }
              ],
              "event": "ptd_dbb:attack"
            }
          ]
        },
        "minecraft:behavior.nearest_prioritized_attackable_target": {
          "priority": 1,
          "must_see": false,
          "attack_interval": 1,
          "reselect_targets": true,
          "must_see_forget_duration": 0,
          "reevaluate_description": true,
          "entity_types": [
            {
              "priority": 0,
              "max_dist": 64,
              "filters": {
                "test": "is_family",
                "subject": "other",
                "value": "player"
              }
            },
            {
              "priority": 1, // Have the minion fight other minions first before targeting other bosses
              "max_dist": 64,
              "filters": {
                "all_of": [
                  {
                    "test": "is_family",
                    "subject": "other",
                    "value": "minion"
                  },
                  {
                    "test": "is_family",
                    "subject": "other",
                    "operator": "!=",
                    "value": "piglin_champion"
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:spawning",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:dead",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  }
                ]
              }
            },
            {
              "priority": 2,
              "max_dist": 64,
              "filters": {
                "all_of": [
                  {
                    "test": "is_family",
                    "subject": "other",
                    "value": "boss"
                  },
                  {
                    "test": "is_family",
                    "subject": "other",
                    "operator": "!=",
                    "value": "piglin_champion"
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:spawning",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:dead",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  }
                ]
              }
            }
          ]
        },
        "minecraft:behavior.hurt_by_target": {
          "priority": 4,
          "entity_types": {
            "max_dist": 64,
            "must_see": false,
            "priority": 0,
            "must_see_forget_duration": 0,
            "filters": {
              "none_of": [
                {
                  "test": "is_family",
                  "subject": "other",
                  "operator": "!=",
                  "value": "piglin_champion"
                }
              ]
            }
          }
        },
        "minecraft:behavior.random_look_around": {
          "priority": 9
        },
        "minecraft:behavior.look_at_player": {
          "priority": 7,
          "look_distance": 6.0,
          "probability": 0.02
        },
        "minecraft:behavior.random_stroll": {
          "priority": 5,
          "speed_multiplier": 0.8
        },
        "minecraft:behavior.melee_attack": {
          "priority": 1,
          "speed_multiplier": 1.15,
          "track_target": true,
          "require_complete_path": false,
          "cooldown_time": 999999,
          "reach_multiplier": 2.0
        },
        "minecraft:damage_sensor": {
          "triggers": [
            {
              "on_damage": {
                "filters": {
                  "test": "actor_health",
                  "subject": "self",
                  "operator": "<=",
                  "value": 2
                },
                "event": "ptd_dbb:dead",
                "target": "self"
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "bool_property",
                  "domain": "ptd_dbb:dead",
                  "subject": "self",
                  "value": true
                }
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "is_family",
                  "subject": "other",
                  "value": "piglin_champion"
                }
              },
              "deals_damage": "no"
            }
          ]
        }
      },
      "ptd_dbb:dead": {
        "minecraft:is_collidable": {},
        "minecraft:timer": {
          "time": 2.0,
          "looping": false,
          "time_down_event": {
            "event": "ptd_dbb:despawn",
            "target": "self"
          }
        },
        "minecraft:movement": {
          "max": 0
        },
        "minecraft:behavior.random_look_around": {
          "priority": 999999
        },
        "minecraft:behavior.random_stroll": {
          "priority": 999999
        },
        "minecraft:body_rotation_blocked": {},
        "minecraft:damage_sensor": {
          "triggers": [
            {
              "on_damage": {
                "filters": {
                  "test": "actor_health",
                  "subject": "self",
                  "operator": "<=",
                  "value": 2
                },
                "event": "ptd_dbb:dead",
                "target": "self"
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "bool_property",
                  "domain": "ptd_dbb:dead",
                  "subject": "self",
                  "value": true
                }
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "is_family",
                  "subject": "other",
                  "value": "piglin_champion"
                }
              },
              "deals_damage": "no"
            }
          ]
        }
      },
      "ptd_dbb:despawn": {
        "minecraft:instant_despawn": {}
      }
    },
    "events": {
      "minecraft:entity_spawned": {
        "add": {
          "component_groups": ["ptd_dbb:spawning"]
        }
      },
      "ptd_dbb:on_spawn": {
        "sequence": [
          {
            "remove": {
              "component_groups": ["ptd_dbb:spawning"]
            }
          },
          {
            "set_property": {
              "ptd_dbb:spawning": false
            }
          },
          {
            "add": {
              "component_groups": ["ptd_dbb:default"]
            }
          }
        ]
      },
      "ptd_dbb:attack": {
        "sequence": [
          {
            "randomize": [
              {
                "weight": 50,
                "set_property": {
                  "ptd_dbb:attack": "slam"
                }
              },
              {
                "weight": 50,
                "set_property": {
                  "ptd_dbb:attack": "sweep"
                }
              }
            ]
          }
        ]
      },
      "ptd_dbb:reset_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "none",
              "ptd_dbb:cooling_down": true
            }
          },
          {
            "add": {
              "component_groups": ["ptd_dbb:default"]
            }
          }
        ]
      },
      "ptd_dbb:dead": {
        "sequence": [
          {
            "set_property": { "ptd_dbb:dead": true }
          },
          {
            "remove": { "component_groups": ["ptd_dbb:default"] }
          },
          {
            "add": {
              "component_groups": ["ptd_dbb:dead"]
            }
          }
        ]
      },
      "ptd_dbb:despawn": {
        "sequence": [
          {
            "add": {
              "component_groups": ["ptd_dbb:despawn"]
            }
          }
        ]
      },
      "ptd_dbb:on_load": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "none",
              "ptd_dbb:cooling_down": false
            }
          },
          {
            "add": {
              "component_groups": ["ptd_dbb:default"]
            }
          }
        ]
      }
    },
    "components": {
      "minecraft:collision_box": {
        "width": 1.0,
        "height": 2.8
      },
      "minecraft:type_family": {
        "family": ["piglin_marauder", "monster", "hostile", "minion", "piglin_champion"]
      },
      "minecraft:damage_sensor": {
        "triggers": [
          {
            "on_damage": {
              "filters": {
                "test": "actor_health",
                "subject": "self",
                "operator": "<=",
                "value": 2
              },
              "event": "ptd_dbb:dead",
              "target": "self"
            },
            "deals_damage": "no"
          },
          {
            "on_damage": {
              "filters": {
                "test": "bool_property",
                "domain": "ptd_dbb:spawning",
                "subject": "self",
                "value": true
              }
            },
            "deals_damage": "no"
          },
          {
            "on_damage": {
              "filters": {
                "test": "bool_property",
                "domain": "ptd_dbb:dead",
                "subject": "self",
                "value": true
              }
            },
            "deals_damage": "no"
          },
          {
            "on_damage": {
              "filters": {
                "test": "is_family",
                "subject": "other",
                "value": "piglin_champion"
              }
            },
            "deals_damage": "no"
          }
        ]
      },
      "minecraft:health": {
        "value": 80,
        "max": 80
      },
      "minecraft:movement": {
        "max": 0.2
      },
      "minecraft:knockback_resistance": {
        "value": 0.8
      },
      "minecraft:underwater_movement": {
        "value": 0.02
      },
      "minecraft:behavior.float": {
        "priority": 0
      },
      "minecraft:variable_max_auto_step": {
        "base_value": 1.0625,
        "jump_prevented_value": 1.0625
      },
      "minecraft:follow_range": {
        "value": 128,
        "max": 128
      },
      "minecraft:pushable": {
        "is_pushable": true,
        "is_pushable_by_piston": true
      },
      "minecraft:conditional_bandwidth_optimization": {}
    }
  }
}
