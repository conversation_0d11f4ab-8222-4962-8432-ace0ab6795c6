{"format_version": "1.21.80", "minecraft:entity": {"description": {"identifier": "ptd_dbb:void_hydra_right_head", "is_spawnable": true, "is_summonable": true, "properties": {"ptd_dbb:spawning": {"type": "bool", "client_sync": true, "default": true}, "ptd_dbb:dead": {"type": "bool", "client_sync": true, "default": false}, "ptd_dbb:attack": {"type": "enum", "client_sync": true, "default": "none", "values": ["none", "right_atomic", "right_atomic_cross", "right_vacuum", "right_summon"]}, "ptd_dbb:cooling_down": {"type": "bool", "client_sync": true, "default": false}}}, "component_groups": {"ptd_dbb:spawning": {"minecraft:is_collidable": {}, "minecraft:timer": {"time": 14, "looping": false, "time_down_event": {"event": "ptd_dbb:on_spawn", "target": "self"}}, "minecraft:damage_sensor": {"triggers": [{"cause": "all", "deals_damage": "no"}]}}, "ptd_dbb:default": {"minecraft:damage_sensor": {"triggers": [{"on_damage": {"filters": {"test": "actor_health", "subject": "self", "operator": "<=", "value": 10}, "event": "ptd_dbb:dead", "target": "self"}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "bool_property", "subject": "self", "domain": "ptd_dbb:dead", "value": true}}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "bool_property", "subject": "self", "domain": "ptd_dbb:spawning", "value": true}}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "is_family", "subject": "other", "value": "void_hydra"}}, "deals_damage": "no"}]}}, "ptd_dbb:targeting": {"minecraft:environment_sensor": {"triggers": [{"filters": [{"test": "target_distance", "subject": "other", "operator": "<=", "value": 64}, {"test": "enum_property", "subject": "self", "domain": "ptd_dbb:attack", "operator": "==", "value": "none"}, {"test": "bool_property", "subject": "self", "domain": "ptd_dbb:cooling_down", "operator": "==", "value": false}], "event": "ptd_dbb:attack"}]}, "minecraft:behavior.look_at_target": {"priority": 2, "look_distance": 64}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 0, "must_see": true, "attack_interval": 1, "reselect_targets": true, "must_see_forget_duration": 0, "reevaluate_description": true, "entity_types": [{"priority": 0, "max_dist": 64, "filters": {"test": "is_family", "subject": "other", "value": "player"}}, {"priority": 1, "max_dist": 64, "filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "boss"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "void_hydra"}, {"test": "bool_property", "domain": "ptd_dbb:spawning", "subject": "other", "operator": "==", "value": false}, {"test": "bool_property", "domain": "ptd_dbb:dead", "subject": "other", "operator": "==", "value": false}]}}, {"priority": 2, "max_dist": 64, "filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "minion"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "void_hydra"}, {"test": "bool_property", "domain": "ptd_dbb:spawning", "subject": "other", "operator": "==", "value": false}, {"test": "bool_property", "domain": "ptd_dbb:dead", "subject": "other", "operator": "==", "value": false}]}}]}}, "ptd_dbb:melee": {"minecraft:behavior.look_at_player": {"priority": 9, "look_distance": 32, "probability": 0.8}, "minecraft:behavior.random_look_around": {"priority": 5}}, "ptd_dbb:dead": {"minecraft:timer": {"time": 500, "looping": false, "time_down_event": {"event": "ptd_dbb:despawn", "target": "self"}}, "minecraft:is_collidable": {}, "minecraft:behavior.random_look_around": {"priority": 999999}, "minecraft:body_rotation_blocked": {}}, "ptd_dbb:despawn": {"minecraft:instant_despawn": {}}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["ptd_dbb:spawning"]}}, "ptd_dbb:on_spawn": {"sequence": [{"remove": {"component_groups": ["ptd_dbb:spawning"]}, "set_property": {"ptd_dbb:spawning": false}}, {"add": {"component_groups": ["ptd_dbb:default", "ptd_dbb:targeting", "ptd_dbb:melee"]}}]}, "ptd_dbb:dead": {"sequence": [{"set_property": {"ptd_dbb:dead": true}}, {"remove": {"component_groups": ["ptd_dbb:targeting", "ptd_dbb:melee"]}}, {"add": {"component_groups": ["ptd_dbb:dead", "ptd_dbb:default"]}}, {"queue_command": {"command": ["scriptevent ptd_dbb:void_hydra_head_death"]}}]}, "ptd_dbb:despawn": {"add": {"component_groups": ["ptd_dbb:despawn"]}}, "ptd_dbb:attack": {}, "ptd_dbb:right_atomic_attack": {"set_property": {"ptd_dbb:attack": "right_atomic"}}, "ptd_dbb:reset_attack": {"set_property": {"ptd_dbb:attack": "none", "ptd_dbb:cooling_down": true}}, "ptd_dbb:on_load": {"sequence": [{"set_property": {"ptd_dbb:attack": "none", "ptd_dbb:cooling_down": false}}, {"add": {"component_groups": ["ptd_dbb:default", "ptd_dbb:targeting", "ptd_dbb:melee"]}}]}}, "components": {"minecraft:type_family": {"family": ["void_hydra_head", "void_hydra", "boss"]}, "minecraft:collision_box": {"width": 0, "height": 0}, "minecraft:custom_hit_test": {"hitboxes": [{"width": 5.3, "height": 14.8, "pivot": [-2.7, 7.4, 2]}]}, "minecraft:health": {"value": 400, "max": 400}, "minecraft:follow_range": {"value": 64, "max": 64}, "minecraft:damage_sensor": {"triggers": [{"on_damage": {"filters": {"test": "bool_property", "domain": "ptd_dbb:dead", "subject": "self", "value": true}}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "is_family", "subject": "other", "value": "void_hydra"}}, "deals_damage": "no"}]}, "minecraft:knockback_resistance": {"value": 1}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:physics": {}, "minecraft:persistent": {}, "minecraft:conditional_bandwidth_optimization": {}}}}