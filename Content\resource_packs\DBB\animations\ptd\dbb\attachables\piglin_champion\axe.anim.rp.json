{"format_version": "1.8.0", "animations": {"animation.ptd_bb.piglin_champion_axe.chop": {"loop": "hold_on_last_frame", "animation_length": 1.25, "bones": {"root": {"rotation": {"0.0": [47.5, 0, 0], "0.1667": [-25, 0, 0], "0.2917": [-20, 0, 0], "0.4167": [-15, 0, 0], "0.5417": [37.5, 0, 0], "0.75": [-30, 0, 0], "0.875": [-32.5, 0, 0], "0.9583": [105, 0, 0], "1.0417": [100, 0, 0], "1.125": [103.5, 0, 0], "1.25": [103.5, 0, 0]}, "position": {"0.0": [0, 25, 4], "0.1667": [0, 28, 4], "0.3333": [0, 27, 4], "0.5": [0, 22, 4], "0.6667": [0, 31, 4], "0.8333": [0, 32, 5], "0.875": [0, 26.7, -1], "0.9167": [0, 11.5, -8.5], "0.9583": [0, 2, -9], "1.0417": [0, 1.5, -9], "1.125": [0, 2, -9], "1.25": [0, 2, -9]}}}}, "animation.ptd_bb.piglin_champion_axe.first_person_hold": {"loop": true, "bones": {"root": {"rotation": [80.92106, 59.6129, -54.69456], "position": [-4, 19, 0], "scale": 0.8}}}, "animation.ptd_bb.piglin_champion_axe.third_person_hold": {"loop": true, "bones": {"root": {"rotation": [87.5, 0, 0], "position": [0, 15, -1], "scale": 0.8}}}}}