{"format_version": "1.10.0", "animations": {"animation.custom_bow.wield": {"loop": true, "bones": {"rightItem": {"position": [" c.is_first_person ? -5.5 : 0.5", "c.is_first_person ? -3 : -2.5", "c.is_first_person ? -3 : 1"], "rotation": ["c.is_first_person ? 38 : 0", "c.is_first_person ? -120 : 0", "c.is_first_person ? -63 : 0"]}}}, "animation.custom_bow.wield_first_person_pull": {"loop": true, "bones": {"rightItem": {"position": [-1.5, " 2.5 + ( v.charge_amount >= 1 ? math.sin((q.life_time) * 1000 * 1.3) * 0.1 - math.sin(q.life_time * 45) * 0.5 : 0)", -4.8], "rotation": [-53, 8, 35]}}}}}