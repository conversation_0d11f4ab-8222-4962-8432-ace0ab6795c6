import { EntityComponentTypes, EquipmentSlot, ItemComponentTypes, ItemStack, system, world } from "@minecraft/server";
export const shovels = new Set([
    'va:bronze_shovel'
]);
export const axes = new Set([
    'va:bronze_axe'
]);
export const hoes = new Set([
    'va:bronze_hoe'
]);
export const pickaxes = new Set([
    'va:bronze_pickaxe'
]);
export const shovelInteractables = new Set([
    'minecraft:grass_block',
    'minecraft:dirt',
    'minecraft:snow',
    'minecraft:snow_layer'
]);
export const hoeInteractables = new Set([
    'minecraft:grass_block',
    'minecraft:dirt',
    'minecraft:grass_path'
]);
export const axeInteractables = new Set([
    'minecraft:oak_log',
    'minecraft:birch_log',
    'minecraft:acacia_log',
    'minecraft:jungle_log',
    'minecraft:spruce_log',
    'minecraft:dark_oak_log',
    'minecraft:mangrove_log',
    'minecraft:cherry_log',
    'minecraft:crimson_stem',
    'minecraft:warped_stem',
    'minecraft:oak_wood',
    'minecraft:birch_wood',
    'minecraft:acacia_wood',
    'minecraft:jungle_wood',
    'minecraft:spruce_wood',
    'minecraft:dark_oak_wood',
    'minecraft:mangrove_wood',
    'minecraft:cherry_wood',
    'minecraft:crimson_hyphae',
    'minecraft:warped_hyphae'
]);
export function damageToolOnUse(player, item, block, blockBreak) {
    const blockId = block.type.id;
    const blockLocation = block.center();
    const dimension = block.dimension.id;
    const location = player.location;
    const mainHandItem = player.getComponent(EntityComponentTypes.Equippable);
    const enchants = mainHandItem.getEquipment(EquipmentSlot.Mainhand).getComponent(ItemComponentTypes.Enchantable).getEnchantments();
    const itemType = item.type;
    const itemId = itemType.id;
    const durabilityComponent = item.getComponent(ItemComponentTypes.Durability);
    const maxDurability = durabilityComponent.maxDurability;
    const currentDamage = durabilityComponent.damage;
    if (currentDamage < maxDurability) {
        if (!blockBreak) {
            if (itemId === 'va:bronze_shovel') {
                switch (blockId) {
                    case 'minecraft:grass_block':
                        block.setType('minecraft:grass_path');
                        world.getDimension(dimension).playSound('step.grass', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:dirt':
                        block.setType('minecraft:grass_path');
                        world.getDimension(dimension).playSound('step.grass', blockLocation);
                        damageTool();
                        break;
                }
            }
            else if (itemId === 'va:bronze_hoe') {
                if (blockId === 'minecraft:grass_block' || blockId === 'minecraft:dirt' || blockId === 'minecraft:grass_path') {
                    block.setType('minecraft:farmland');
                    world.getDimension(dimension).playSound('hit.gravel', blockLocation);
                    damageTool();
                }
            }
            else if (itemId === 'va:bronze_axe') {
                switch (blockId) {
                    case 'minecraft:oak_log':
                        block.setType('minecraft:stripped_oak_log');
                        world.getDimension(dimension).playSound('step.wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:birch_log':
                        block.setType('minecraft:stripped_birch_log');
                        world.getDimension(dimension).playSound('step.wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:acacia_log':
                        block.setType('minecraft:stripped_acacia_log');
                        world.getDimension(dimension).playSound('step.wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:jungle_log':
                        block.setType('minecraft:stripped_jungle_log');
                        world.getDimension(dimension).playSound('step.wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:spruce_log':
                        block.setType('minecraft:stripped_spruce_log');
                        world.getDimension(dimension).playSound('step.wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:dark_oak_log':
                        block.setType('minecraft:stripped_dark_oak_log');
                        world.getDimension(dimension).playSound('step.wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:mangrove_log':
                        block.setType('minecraft:stripped_mangrove_log');
                        world.getDimension(dimension).playSound('step.wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:cherry_log':
                        block.setType('minecraft:stripped_cherry_log');
                        world.getDimension(dimension).playSound('step.wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:crimson_stem':
                        block.setType('minecraft:stripped_crimson_stem');
                        world.getDimension(dimension).playSound('break.nether_wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:warped_stem':
                        block.setType('minecraft:stripped_warped_stem');
                        world.getDimension(dimension).playSound('break.nether_wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:oak_wood':
                        block.setType('minecraft:stripped_oak_wood');
                        world.getDimension(dimension).playSound('step.wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:birch_wood':
                        block.setType('minecraft:stripped_birch_wood');
                        world.getDimension(dimension).playSound('step.wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:acacia_wood':
                        block.setType('minecraft:stripped_acacia_wood');
                        world.getDimension(dimension).playSound('step.wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:jungle_wood':
                        block.setType('minecraft:stripped_jungle_wood');
                        world.getDimension(dimension).playSound('step.wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:spruce_wood':
                        block.setType('minecraft:stripped_spruce_wood');
                        world.getDimension(dimension).playSound('step.wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:dark_oak_wood':
                        block.setType('minecraft:stripped_dark_oak_wood');
                        world.getDimension(dimension).playSound('step.wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:mangrove_wood':
                        block.setType('minecraft:stripped_mangrove_wood');
                        world.getDimension(dimension).playSound('step.wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:cherry_wood':
                        block.setType('minecraft:stripped_cherry_wood');
                        world.getDimension(dimension).playSound('step.wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:crimson_hyphae':
                        block.setType('minecraft:stripped_crimson_hyphae');
                        world.getDimension(dimension).playSound('break.nether_wood', blockLocation);
                        damageTool();
                        break;
                    case 'minecraft:warped_hyphae':
                        block.setType('minecraft:stripped_warped_hyphae');
                        world.getDimension(dimension).playSound('break.nether_wood', blockLocation);
                        damageTool();
                        break;
                }
            }
        }
        else {
            if (itemId.startsWith('va:bronze')) {
                let removeDiamonds = system.runTimeout(() => {
                    player.dimension.getEntities({ location: location, maxDistance: 6, type: 'minecraft:item' }).forEach(item => {
                        const typeId = item.getComponent(EntityComponentTypes.Item).itemStack.typeId;
                        if (typeId === 'minecraft:diamond') {
                            player.dimension.getEntities({ location: location, maxDistance: 6, type: 'minecraft:xp_orb' }).forEach(xp => {
                                xp.remove();
                            });
                            item.remove();
                        }
                    });
                    system.clearRun(removeDiamonds);
                });
            }
            if (itemId === 'va:bronze_shovel') {
                switch (blockId) {
                    case 'minecraft:snow':
                        player.dimension.spawnItem(new ItemStack('minecraft:snowball', 8), blockLocation);
                        break;
                    case 'minecraft:snow_layer':
                        player.dimension.spawnItem(new ItemStack('minecraft:snowball', 4), blockLocation);
                        break;
                }
            }
            damageTool();
        }
    }
    else {
        world.getDimension(dimension).playSound('random.break', location);
        mainHandItem.setEquipment(EquipmentSlot.Mainhand, new ItemStack('minecraft:air', 1));
    }
    function damageTool() {
        const newItem = new ItemStack(itemType, 1);
        const newDurabilityComponent = newItem.getComponent(ItemComponentTypes.Durability);
        newDurabilityComponent.damage = currentDamage + 1;
        enchants.forEach((enchant) => {
            newItem.getComponent(ItemComponentTypes.Enchantable).addEnchantment(enchant);
        });
        mainHandItem.setEquipment(EquipmentSlot.Mainhand, newItem);
        return;
    }
    return;
}
