{"format_version": "1.20.50", "minecraft:attachable": {"description": {"identifier": "ptd_bb:piglin_champion_axe", "materials": {"default": "entity_alphatest", "enchanted": "entity_alphatest_glint"}, "textures": {"default": "textures/ptd/bb/attachables/items/piglin_champion/axe", "enchanted": "textures/misc/enchanted_item_glint"}, "geometry": {"default": "geometry.ptd_bb_piglin_champion_axe"}, "animations": {"first_person_hold": "animation.ptd_bb.piglin_champion_axe.first_person_hold", "third_person_hold": "animation.ptd_bb.piglin_champion_axe.third_person_hold", "chop": "animation.ptd_bb.piglin_champion_axe.chop", "ground_slam": "controller.animation.ptd_bb.piglin_champion_axe.ground_slam"}, "scripts": {"animate": [{"ground_slam": "c.is_first_person"}, {"first_person_hold": "c.is_first_person"}, {"third_person_hold": "!c.is_first_person"}]}, "render_controllers": ["controller.render.item_default"]}}}