{"format_version": "1.21.30", "minecraft:block": {"description": {"identifier": "va:fermentation_barrel", "menu_category": {"category": "items"}, "states": {"va:fermenting": [false, true], "va:ready_to_collect": [false, true], "va:ferment": ["none", "mead", "wine", "ale"], "va:ferment_day": {"values": {"min": 0, "max": 3}}, "va:fermented_items": {"values": {"min": 0, "max": 15}}}}, "components": {"minecraft:collision_box": true, "minecraft:selection_box": true, "minecraft:custom_components": ["va:ferment"], "minecraft:destructible_by_mining": {"seconds_to_destroy": 2.4}, "minecraft:transformation": {"rotation": [0, 0, 0]}, "minecraft:tick": {"interval_range": [20, 24], "looping": true}, "minecraft:destructible_by_explosion": {"explosion_resistance": 15}, "minecraft:flammable": {"destroy_chance_modifier": 0, "catch_chance_modifier": 5}, "minecraft:geometry": "geometry.fermentation_barrel", "minecraft:material_instances": {"*": {"texture": "fermentation_barrel_fermenting", "render_method": "blend"}}, "minecraft:map_color": "#5f4a2b"}, "permutations": [{"condition": "q.block_state('va:ready_to_collect') == true", "components": {"minecraft:tick": {"interval_range": [90, 140], "looping": true}}}, {"condition": "q.block_state('va:fermenting') == true", "components": {"minecraft:material_instances": {"*": {"texture": "fermentation_barrel_fermenting", "render_method": "blend"}, "liquid_layers": {"render_method": "blend"}}}}, {"condition": "q.block_state('va:fermenting') == false", "components": {"minecraft:material_instances": {"*": {"texture": "fermentation_barrel_empty", "render_method": "blend"}, "liquid_layers": {"render_method": "blend"}}}}, {"condition": "q.block_state('va:fermented_items') == 15", "components": {"minecraft:material_instances": {"*": {"texture": "fermentation_barrel_layer15", "render_method": "blend"}}}}, {"condition": "q.block_state('va:fermented_items') == 14", "components": {"minecraft:material_instances": {"*": {"texture": "fermentation_barrel_layer14", "render_method": "blend"}}}}, {"condition": "q.block_state('va:fermented_items') == 13", "components": {"minecraft:material_instances": {"*": {"texture": "fermentation_barrel_layer13", "render_method": "blend"}}}}, {"condition": "q.block_state('va:fermented_items') == 12", "components": {"minecraft:material_instances": {"*": {"texture": "fermentation_barrel_layer12", "render_method": "blend"}}}}, {"condition": "q.block_state('va:fermented_items') == 11", "components": {"minecraft:material_instances": {"*": {"texture": "fermentation_barrel_layer11", "render_method": "blend"}}}}, {"condition": "q.block_state('va:fermented_items') == 10", "components": {"minecraft:material_instances": {"*": {"texture": "fermentation_barrel_layer10", "render_method": "blend"}}}}, {"condition": "q.block_state('va:fermented_items') == 9", "components": {"minecraft:material_instances": {"*": {"texture": "fermentation_barrel_layer9", "render_method": "blend"}}}}, {"condition": "q.block_state('va:fermented_items') == 8", "components": {"minecraft:material_instances": {"*": {"texture": "fermentation_barrel_layer8", "render_method": "blend"}}}}, {"condition": "q.block_state('va:fermented_items') == 7", "components": {"minecraft:material_instances": {"*": {"texture": "fermentation_barrel_layer7", "render_method": "blend"}}}}, {"condition": "q.block_state('va:fermented_items') == 6", "components": {"minecraft:material_instances": {"*": {"texture": "fermentation_barrel_layer6", "render_method": "blend"}}}}, {"condition": "q.block_state('va:fermented_items') == 5", "components": {"minecraft:material_instances": {"*": {"texture": "fermentation_barrel_layer5", "render_method": "blend"}}}}, {"condition": "q.block_state('va:fermented_items') == 4", "components": {"minecraft:material_instances": {"*": {"texture": "fermentation_barrel_layer4", "render_method": "blend"}}}}, {"condition": "q.block_state('va:fermented_items') == 3", "components": {"minecraft:material_instances": {"*": {"texture": "fermentation_barrel_layer3", "render_method": "blend"}}}}, {"condition": "q.block_state('va:fermented_items') == 2", "components": {"minecraft:material_instances": {"*": {"texture": "fermentation_barrel_layer2", "render_method": "blend"}}}}, {"condition": "q.block_state('va:fermented_items') == 1", "components": {"minecraft:material_instances": {"*": {"texture": "fermentation_barrel_layer2", "render_method": "blend"}}}}]}}