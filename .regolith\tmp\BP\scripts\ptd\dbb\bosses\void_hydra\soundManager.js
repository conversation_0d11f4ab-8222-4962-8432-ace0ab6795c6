/**
 * List of all sound effects for the Void Hydra
 * Note: These are placeholders and should be updated when sound effects are added
 */
export const VOID_HYDRA_SOUNDS = [
    "mob.ptd_dbb_void_hydra.death",
    "mob.ptd_dbb_void_hydra.idle",
    "mob.ptd_dbb_void_hydra.spawn",
    "mob.ptd_dbb_void_hydra.move",
    // Attack sounds
    "mob.ptd_dbb_void_hydra.right_atomic_cross",
    "mob.ptd_dbb_void_hydra.right_atomic",
    "mob.ptd_dbb_void_hydra.right_vacuum",
    "mob.ptd_dbb_void_hydra.right_summon",
    "mob.ptd_dbb_void_hydra.mid_atomic",
    "mob.ptd_dbb_void_hydra.mid_meteor",
    "mob.ptd_dbb_void_hydra.mid_singularity",
    "mob.ptd_dbb_void_hydra.left_atomic_cross",
    "mob.ptd_dbb_void_hydra.left_atomic",
    "mob.ptd_dbb_void_hydra.left_railgun",
    "mob.ptd_dbb_void_hydra.left_missile",
    "mob.ptd_dbb_void_hydra.left_shout"
];
/**
 * Maps attack names to their corresponding sound effects
 * Note: These are placeholders and should be updated when sound effects are added
 */
export const VOID_HYDRA_ATTACK_SOUND_MAP = {
    none: "",
    right_atomic_cross: "mob.ptd_dbb_void_hydra.right_atomic_cross",
    right_atomic: "mob.ptd_dbb_void_hydra.right_atomic",
    right_vacuum: "mob.ptd_dbb_void_hydra.right_vacuum",
    right_summon: "mob.ptd_dbb_void_hydra.right_summon",
    mid_atomic: "mob.ptd_dbb_void_hydra.mid_atomic",
    mid_meteor: "mob.ptd_dbb_void_hydra.mid_meteor",
    mid_singularity: "mob.ptd_dbb_void_hydra.mid_singularity",
    left_atomic_cross: "mob.ptd_dbb_void_hydra.left_atomic_cross",
    left_atomic: "mob.ptd_dbb_void_hydra.left_atomic",
    left_railgun: "mob.ptd_dbb_void_hydra.left_railgun",
    left_missile: "mob.ptd_dbb_void_hydra.left_missile",
    left_shout: "mob.ptd_dbb_void_hydra.left_shout"
};
/**
 * Stops all Void Hydra sound effects except the excluded one
 * Uses the stopsound command for more effective sound stopping
 *
 * @param entity The entity to stop sounds for
 * @param excludedSound Optional sound to exclude from stopping
 */
export function stopVoidHydraSounds(entity, excludedSound) {
    try {
        if (!entity)
            return;
        // Use the stopsound command to stop all sounds except the excluded one
        for (const sound of VOID_HYDRA_SOUNDS) {
            // Skip the excluded sound
            if (excludedSound && sound === excludedSound)
                continue;
            // Use the stopsound command to stop the sound
            try {
                entity.runCommand(`stopsound @a ${sound}`);
            }
            catch (cmdError) {
                // If the command fails, log the error but continue with other sounds
                console.warn(`Error running stopsound command for ${sound}: ${cmdError}`);
            }
        }
    }
    catch (error) {
        console.warn(`Error stopping Void Hydra sounds: ${error}`);
    }
}
