/**
 * Grimhowl boss left claw attack implementation.
 * This file contains the logic for executing the left claw attack,
 * including animation, sound effects, and damage application.
 */
import { system, EntityDamageCause } from "@minecraft/server";
import { isEntityInFront } from "../../general_mechanics/targetUtils";
import { GRIMHOWL_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
/**
 * Executes the left claw attack for the Grimhowl boss
 * The attack faces the boss toward a target, plays an animation,
 * and damages entities in front of the boss
 *
 * @param sourceEntity - The Grimhowl boss entity
 * @param frontTargets - Array of entities in front of the boss
 */
export function doGrimhowlLeftClaw(sourceEntity, nearbyTargets) {
    try {
        if (!sourceEntity)
            return;
        const isEnraged = sourceEntity.getProperty('ptd_dbb:enraged');
        const isSwordMode = sourceEntity.getProperty('ptd_dbb:sword_mode');
        if (isEnraged) {
            sourceEntity.triggerEvent('ptd_dbb:grimhowl_claw_left_enraged');
        }
        else {
            sourceEntity.triggerEvent('ptd_dbb:grimhowl_claw_left');
        }
        // Check if sourceEntity is still valid before teleporting
        if (!sourceEntity)
            return;
        // Check if a frontTarget to face to, if not, face defaultFacingLocation
        sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.grunt @a ~ ~ ~ 5${1 + (Math.random() * 0.3)}`);
        system.runTimeout(() => {
            if (!sourceEntity)
                return;
            const updatedFrontTargets = nearbyTargets.filter((entity) => {
                return isEntityInFront(sourceEntity, entity, 0.75);
            });
            updatedFrontTargets.forEach((entity) => {
                entity.applyDamage(GRIMHOWL_ATTACK_DAMAGES.left_claw.damage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
                entity.applyKnockback(sourceEntity.getViewDirection().x, sourceEntity.getViewDirection().z, isSwordMode ? 2.5 : -2.5, 0.25);
            });
        }, isEnraged ? 9 : 18);
        system.runTimeout(() => {
            if (!sourceEntity)
                return;
            sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.claw_attack @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
        }, isEnraged ? 0.75 * 10 : 0.75 * 20);
        system.runTimeout(() => {
            if (!sourceEntity)
                return;
            sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.pre_claw @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
        }, isEnraged ? 0.75 * 10 : 0.75 * 20);
    }
    catch (error) {
        sourceEntity?.triggerEvent('ptd_dbb:attack_done');
    }
}
