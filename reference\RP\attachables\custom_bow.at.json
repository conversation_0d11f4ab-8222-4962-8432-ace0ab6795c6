{"format_version": "1.10.0", "minecraft:attachable": {"description": {"identifier": "va:custom_bow", "min_engine_version": "1.8.0", "materials": {"default": "entity_alphatest", "enchanted": "entity_alphatest_glint"}, "textures": {"bow_standby": "textures/items/bows/custom_bow/bow_standby", "bow_pulling_0": "textures/items/bows/custom_bow/bow_pulling_0", "bow_pulling_1": "textures/items/bows/custom_bow/bow_pulling_1", "bow_pulling_2": "textures/items/bows/custom_bow/bow_pulling_2", "enchanted": "textures/misc/enchanted_item_glint"}, "geometry": {"bow_standby": "geometry.custom_bow_standby", "bow_pulling_0": "geometry.custom_bow_pulling_0", "bow_pulling_1": "geometry.custom_bow_pulling_1", "bow_pulling_2": "geometry.custom_bow_pulling_2"}, "animations": {"wield": "animation.custom_bow.wield", "wield_first_person_pull": "animation.custom_bow.wield_first_person_pull"}, "scripts": {"pre_animation": ["v.charge_amount = 0;", "t.arrow_inv = 0; t.i = 0; loop(27, {t.iron_arrow_inv = (q.is_item_name_any('slot.inventory', t.i, 'minecraft:arrow') || q.is_item_name_any('slot.hotbar', t.i, 'minecraft:arrow') || q.is_item_name_any('slot.weapon.offhand', 0, 'minecraft:arrow')); t.iron_arrow_inv ? {v.charge_amount = q.main_hand_item_use_duration > 0.0f ? (math.clamp((q.main_hand_item_max_duration - q.main_hand_item_use_duration) / 5, 1, 3)) : 0;}; t.i = t.i+1;});", "t.iron_arrow_inv = 0; t.i = 0; loop(27, {t.iron_arrow_inv = (q.is_item_name_any('slot.inventory', t.i, 'va:iron_arrow') || q.is_item_name_any('slot.hotbar', t.i, 'va:iron_arrow') || q.is_item_name_any('slot.weapon.offhand', 0, 'va:iron_arrow')); t.iron_arrow_inv ? {v.charge_amount = q.main_hand_item_use_duration > 0.0f ? (math.clamp((q.main_hand_item_max_duration - q.main_hand_item_use_duration) / 5, 1, 3)) : 0;}; t.i = t.i+1;});"], "animate": ["wield", {"wield_first_person_pull": "v.charge_amount > 0.0f && c.is_first_person"}]}, "render_controllers": ["controller.render.custom_bow"]}}}