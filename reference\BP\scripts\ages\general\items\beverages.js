import { EntityComponentTypes, EquipmentSlot } from "@minecraft/server";
import { decrementStackMainHand } from "./general";
export const drunkThreshold = new Map();
const beverageEffects = {
    mead: { effectId: 'minecraft:strength', duration: 600, amplifier: 1 },
    wine: { effectId: 'minecraft:resistance', duration: 600, amplifier: 1 },
    ale: { effectId: 'minecraft:regeneration', duration: 600, amplifier: 1 },
};
const tipsyMessages = [
    "I'm feeling a bit tipsy...",
    "I'm feeling a bit lightheaded...",
    "Whoa, the world is spinning!",
    "Maybe I've had enough for now.",
    "I should sit down for a moment.",
    "Everything feels so funny!",
    "Time seems to be moving slower.",
    "Is it me or is the ground shaking?",
    "I can't feel my face!",
    "That last drink was strong!",
    "I might regret this tomorrow.",
    "Maybe some water would be good.",
    "I shouldn't have mixed drinks.",
    "Why is everything so blurry?",
    "I think I need a nap.",
    "Am I talking too loud?",
    "My head feels fuzzy.",
    "My balance is off...",
    "Everything is so funny!",
    "I'm seeing double!",
    "I need to hold onto something.",
    "My legs feel wobbly.",
    "I think I'm floating.",
    "Time for another round!",
    "What's so funny?",
    "Let's go on an adventure!",
    "I forgot what I was saying...",
    "Did I say that out loud?",
    "Nothing can stop me now!",
    "The stars are so bright tonight.",
    "I could use some fresh air.",
    "I can't feel my feet.",
    "Is the room spinning?",
    "I think I need some sleep.",
    "I might sit down for a bit.",
    "Let's make some memories!",
    "I'm feeling so carefree.",
    "Nothing makes sense anymore.",
    "I could run a marathon!",
    "I wish this night would never end.",
    "Is it raining, or is that just me?",
    "I think I need a glass of water.",
    "Everything feels so light.",
    "I forgot where I was going.",
    "I think I'm talking to myself.",
    "I'm on top of the world!",
    "I could use a nap.",
    "The colors are so vivid!",
    "I feel like I'm flying!",
    "Did the sky always look this beautiful?",
];
export function onConsumeBeverage(player, beverage) {
    const beverageType = Object.keys(beverageEffects).find((type) => beverage.endsWith(type));
    if (beverageType) {
        const { effectId, duration, amplifier } = beverageEffects[beverageType];
        applyOrExtendEffect(player, effectId, duration, amplifier);
    }
    const drunkLevel = (drunkThreshold.get(player.id) || 0) + 1;
    drunkThreshold.set(player.id, drunkLevel);
    if (drunkLevel >= 5) {
        applyOrExtendEffect(player, 'minecraft:nausea', 600, 1);
        applyOrExtendEffect(player, 'minecraft:slowness', 600, 1);
        drunkThreshold.set(player.id, 0);
        const randomIndex = Math.floor(Math.random() * tipsyMessages.length);
        const randomMessage = tipsyMessages[randomIndex];
        player.sendMessage(`[${player.name}] ${randomMessage}`);
    }
    return;
}
function applyOrExtendEffect(player, effectId, duration, amplifier) {
    const currentEffect = player.getEffect(effectId);
    if (currentEffect) {
        const newDuration = currentEffect.duration + duration;
        addEffect(player, effectId, newDuration, amplifier);
    }
    else {
        addEffect(player, effectId, duration, amplifier);
    }
    return;
}
function addEffect(player, effectId, duration, amplifier) {
    player.addEffect(effectId, duration, {
        amplifier: amplifier,
        showParticles: true,
    });
    return;
}
export function hitWithBeerGlass(player, entityHit) {
    const equippable = player.getComponent(EntityComponentTypes.Equippable);
    const mainHand = equippable.getEquipmentSlot(EquipmentSlot.Mainhand);
    const mainHandItem = mainHand.getItem();
    decrementStackMainHand(mainHand, mainHandItem);
    entityHit.dimension.playSound('random.glass', entityHit.location);
    entityHit.dimension.spawnParticle('va:beer_glass_break', entityHit.location);
    return;
}
