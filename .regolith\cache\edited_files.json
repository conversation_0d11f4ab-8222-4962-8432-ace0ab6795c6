{"rp": {"C:\\Users\\<USER>\\AppData\\Local\\Packages\\Microsoft.MinecraftUWP_8wekyb3d8bbwe\\LocalState\\games\\com.mojang/development_resource_packs/Dr. Berry's Bosses_rp": ["animation_controllers\\ptd\\dbb\\attachables\\piglin_champion\\axe.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\grimhowl.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\necromancer\\general.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\necromancer\\look_at_target.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\necromancer\\minions\\s_soul.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\necromancer\\minions\\w_zombie.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\necromancer\\minions\\zombie_brute.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\necromancer\\soul_trap.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\piglin_champion\\general.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\piglin_champion\\look_at_target.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\piglin_champion\\minions\\brute.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\piglin_champion\\minions\\marauder.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\piglin_champion\\stun.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\void_hydra\\attack.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\void_hydra\\general.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\void_hydra\\heads\\right_head.ac.rp.json", "animation_controllers\\ptd\\dbb\\others\\piglin_champion_minion.ac.rp.json", "animations\\ptd\\dbb\\attachables\\piglin_champion\\axe.anim.rp.json", "animations\\ptd\\dbb\\attachables\\piglin_champion\\goblet.anim.rp.json", "animations\\ptd\\dbb\\bosses\\grimhowl\\grimhowl.anim.rp.json", "animations\\ptd\\dbb\\bosses\\grimhowl\\minions\\spirit_wolf.anim.rp.json", "animations\\ptd\\dbb\\bosses\\grimhowl\\wolf_boss.anim.rp.json", "animations\\ptd\\dbb\\bosses\\necromancer\\cataclysm.anim.rp.json", "animations\\ptd\\dbb\\bosses\\necromancer\\minions\\winged_zombie.anim.rp.json", "animations\\ptd\\dbb\\bosses\\necromancer\\minions\\zombie_brute.anim.rp.json", "animations\\ptd\\dbb\\bosses\\necromancer\\necromancer.anim.rp.json", "animations\\ptd\\dbb\\bosses\\necromancer\\skeleton_soul.anim.rp.json", "animations\\ptd\\dbb\\bosses\\necromancer\\soul_trap.anim.rp.json", "animations\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_brute.anim.rp.json", "animations\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_marauder.anim.rp.json", "animations\\ptd\\dbb\\bosses\\piglin_champion\\piglin_champion.anim.rp.json", "animations\\ptd\\dbb\\bosses\\piglin_champion\\rock.anim.rp.json", "animations\\ptd\\dbb\\bosses\\void_hydra\\heads\\right_head.anim.rp.json", "animations\\ptd\\dbb\\bosses\\void_hydra\\void_hydra.anim.rp.json", "animations\\ptd\\dbb\\bosses\\wardzilla\\wardzilla.anim.rp.json", "animations\\ptd\\dbb\\guidebook\\guidebook.anim.rp.json", "animations\\ptd\\dbb\\projectiles\\flying_skull.anim.rp.json", "animations\\ptd\\dbb\\projectiles\\general.anim.rp.json", "attachables\\ptd\\dbb\\grim_howl\\armor\\boots.attachable.json", "attachables\\ptd\\dbb\\grim_howl\\armor\\chestplate.attachable.json", "attachables\\ptd\\dbb\\grim_howl\\armor\\helmet.attachable.json", "attachables\\ptd\\dbb\\grim_howl\\armor\\leggings.attachable.json", "attachables\\ptd\\dbb\\necromancer\\armor\\boots.attachable.json", "attachables\\ptd\\dbb\\necromancer\\armor\\chestplate.attachable.json", "attachables\\ptd\\dbb\\necromancer\\armor\\helmet.attachable.json", "attachables\\ptd\\dbb\\necromancer\\armor\\leggings.attachable.json", "attachables\\ptd\\dbb\\piglin_champion\\armor\\boots.attachable.json", "attachables\\ptd\\dbb\\piglin_champion\\armor\\chestplate.attachable.json", "attachables\\ptd\\dbb\\piglin_champion\\armor\\helmet.attachable.json", "attachables\\ptd\\dbb\\piglin_champion\\armor\\leggings.attachable.json", "attachables\\ptd\\dbb\\piglin_champion\\items\\axe.attachable.json", "attachables\\ptd\\dbb\\piglin_champion\\items\\goblet.attachable.json", "attachables\\ptd\\dbb\\void_hydra\\armor\\boots.attachable.json", "attachables\\ptd\\dbb\\void_hydra\\armor\\chestplate.attachable.json", "attachables\\ptd\\dbb\\void_hydra\\armor\\helmet.attachable.json", "attachables\\ptd\\dbb\\void_hydra\\armor\\leggings.attachable.json", "attachables\\ptd\\dbb\\wardzilla\\armor\\boots.attachable.json", "attachables\\ptd\\dbb\\wardzilla\\armor\\chestplate.attachable.json", "attachables\\ptd\\dbb\\wardzilla\\armor\\helmet.attachable.json", "attachables\\ptd\\dbb\\wardzilla\\armor\\leggings.attachable.json", "entity\\ptd\\dbb\\bosses\\grimhowl.e.rp.json", "entity\\ptd\\dbb\\bosses\\necromancer\\minions\\skeleton_soul.e.rp.json", "entity\\ptd\\dbb\\bosses\\necromancer\\minions\\winged_zombie.e.rp.json", "entity\\ptd\\dbb\\bosses\\necromancer\\minions\\zombie_brute.e.rp.json", "entity\\ptd\\dbb\\bosses\\necromancer\\necromancer.e.rp.json", "entity\\ptd\\dbb\\bosses\\necromancer\\soul_trap.e.rp.json", "entity\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_brute.e.rp.json", "entity\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_marauder.e.rp.json", "entity\\ptd\\dbb\\bosses\\piglin_champion\\piglin_champion.e.rp.json", "entity\\ptd\\dbb\\bosses\\piglin_champion\\rock.e.rp.json", "entity\\ptd\\dbb\\bosses\\void_hydra\\heads\\right_head.e.rp.json", "entity\\ptd\\dbb\\bosses\\void_hydra\\void_hydra.e.rp.json", "entity\\ptd\\dbb\\projectiles\\flying_skull.e.rp.json", "manifest.json", "materials\\entity.material", "models\\entity\\attachables\\grim_howl\\armor\\boots.geo.json", "models\\entity\\attachables\\grim_howl\\armor\\chestplate.geo.json", "models\\entity\\attachables\\grim_howl\\armor\\helmet.geo.json", "models\\entity\\attachables\\grim_howl\\armor\\leggings.geo.json", "models\\entity\\attachables\\necromancer\\armor\\boots.geo.json", "models\\entity\\attachables\\necromancer\\armor\\chestplate.geo.json", "models\\entity\\attachables\\necromancer\\armor\\helmet.geo.json", "models\\entity\\attachables\\necromancer\\armor\\leggings.geo.json", "models\\entity\\attachables\\necromancer\\items\\soul_book.geo.json", "models\\entity\\attachables\\piglin_champion\\armor\\boots.geo.json", "models\\entity\\attachables\\piglin_champion\\armor\\chestplate.geo.json", "models\\entity\\attachables\\piglin_champion\\armor\\helmet.geo.json", "models\\entity\\attachables\\piglin_champion\\armor\\leggings.geo.json", "models\\entity\\attachables\\piglin_champion\\items\\axe.geo.json", "models\\entity\\attachables\\piglin_champion\\items\\goblet.geo.json", "models\\entity\\attachables\\void_hydra\\armor\\boots.geo.json", "models\\entity\\attachables\\void_hydra\\armor\\chestplate.geo.json", "models\\entity\\attachables\\void_hydra\\armor\\helmet.geo.json", "models\\entity\\attachables\\void_hydra\\armor\\leggings.geo.json", "models\\entity\\attachables\\void_hydra\\items\\void_scepter.geo.json", "models\\entity\\attachables\\wardzilla\\armor\\boots.geo.json", "models\\entity\\attachables\\wardzilla\\armor\\chestplate.geo.json", "models\\entity\\attachables\\wardzilla\\armor\\helmet.geo.json", "models\\entity\\attachables\\wardzilla\\armor\\leggings.geo.json", "models\\entity\\bosses\\grimhowl\\grimhowl.geo.json", "models\\entity\\bosses\\grimhowl\\grimhowl_fx.geo.json", "models\\entity\\bosses\\grimhowl\\minions\\spirit_wolf.geo.json", "models\\entity\\bosses\\necromancer\\cataclysm.geo.json", "models\\entity\\bosses\\necromancer\\minions\\winged_zombie.geo.json", "models\\entity\\bosses\\necromancer\\minions\\zombie_brute.geo.json", "models\\entity\\bosses\\necromancer\\necromancer.geo.json", "models\\entity\\bosses\\necromancer\\skeleton_soul.geo.json", "models\\entity\\bosses\\necromancer\\soul_trap.geo.json", "models\\entity\\bosses\\piglin_champion\\minions\\piglin_brute.geo.json", "models\\entity\\bosses\\piglin_champion\\minions\\piglin_marauder.geo.json", "models\\entity\\bosses\\piglin_champion\\piglin_champion.geo.json", "models\\entity\\bosses\\piglin_champion\\rock.geo.json", "models\\entity\\bosses\\void_hydra\\heads\\right_head.geo.json", "models\\entity\\bosses\\void_hydra\\void_hydra.geo.json", "models\\entity\\bosses\\wardzilla\\wardzilla.geo.json", "models\\entity\\bosses\\wardzilla\\weapons\\sculk_slammer.geo.json", "models\\entity\\bosses\\wardzilla\\weapons\\sculk_slammer.png", "models\\entity\\guidebook.geo.json", "models\\entity\\projectiles\\flying_skull.geo.json", "pack_icon.png", "particles\\ptd\\dbb\\necromaner\\nec_arcane1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_arcane1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_arcane2_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_arcane2_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_arcane2_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_arcane3_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_book1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_book1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_book1_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_castinghand1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_castinghand1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_cata1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_cata1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_cata1_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_cata1_04.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_die1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_die1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_die1_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_head1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_head4_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_idle1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_idle1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_idle1_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_phantom1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_phantom1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_phantom1_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_phantom2_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_phantom2_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_phantom2_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_souldrain1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_souldrain1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_souldrain1_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_souldrain1_04.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_souldrain1_05.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_souldrain1_06.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_souldrain2_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_soulhands1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_soulhands1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_soulhands1_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_soulhands1_04.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_spawn1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_spawn1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_spawn1_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_spawn1_04.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_spawn1_05.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_summon1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_summon1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_axe1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_axe2_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_body_slam1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_body_slam1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_body_slam1_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_body_slam1_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_body_slam1_05.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_body_slam1_06.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_burp1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_burp1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_burp1_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_charge1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_charge1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_charge1_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_charge2_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_cup1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die1_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die1_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die1_05.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die1_06.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die2_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die2_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die2_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_foot_stomp1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_foot_stomp1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_foot_stomp1_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_foot_stomp1_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_foot_stomp1_05.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_foot_stomp1_06.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_heal1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_heal1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_heal1_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_heal2_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_heal3_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_heal3_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_schant1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_05.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_06.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_07.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_08.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_09.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_10.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn3_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn3_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn3_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn3_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn3_05.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn3_06.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn3_07.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam2_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam2_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam3_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam4_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam4_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam4_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam4_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam4_05.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_stunned1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_stunned1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_stunned2_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_stunned2_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_stunned3_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon1_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon1_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon2_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon2_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon2_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon2_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon3_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon3_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon3_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon3_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_upchuck1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_upchuck1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_upchuck1_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_upchuck1_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_upchuck1_05.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_upchuck1_06.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_upchuck1_07.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_upchuck1_08.particle.json", "render_controllers\\ptd\\dbb\\entity\\default.rc.json", "render_controllers\\ptd\\dbb\\entity\\default_emissive.rc.json", "render_controllers\\ptd\\dbb\\entity\\grimhowl.rc.rp.json", "render_controllers\\ptd\\dbb\\entity\\necromancer.rc.json", "render_controllers\\ptd\\dbb\\entity\\skeleton_soul.rc.json", "render_controllers\\ptd\\dbb\\entity\\void_hydra.rc.json", "render_controllers\\ptd\\dbb\\entity\\winged_zombie.rc.json", "render_controllers\\ptd\\dbb\\entity\\zombie_brute.rc.json", "sounds\\ptd\\dbb\\bosses\\grimhowl\\claw_attack.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\dodge.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\grunt.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\grunt_2.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\idle.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\idle_2.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\idle_3.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\pounce_attack.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\pounce_attack_swordless.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\pre_claw.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\pre_pounce.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\roar.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\spawn.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\sword_swipe.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\sword_swipe_2.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\arcane_blast.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\cataclysm.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\death.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\idle.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\move.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\phantom_phase_end.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\phantom_phase_start.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\soul_drain.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\soul_hands.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\spawn.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\undead_summon.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\body_slam.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\charging.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\damaged_to_stunned.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\death.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\foot_stomp.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\healing.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\horizontal_attack.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\idle.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\minion_atk1.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\minion_atk2.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\spawn.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\spin_slam.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\stunned_standing.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\stunned_to_idle.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\summoning_chant.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\upchuck.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\vertical_attack.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\walk1.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\walk2.ogg", "sounds\\sound_definitions.json", "sounds.json", "texts\\en_US.lang", "texts\\languages.json", "textures\\flipbook_textures.json", "textures\\item_texture.json", "textures\\ptd\\dbb\\attachables\\armor\\grim_howl\\boots.png", "textures\\ptd\\dbb\\attachables\\armor\\grim_howl\\chestplate.png", "textures\\ptd\\dbb\\attachables\\armor\\grim_howl\\helmet.png", "textures\\ptd\\dbb\\attachables\\armor\\grim_howl\\leggings.png", "textures\\ptd\\dbb\\attachables\\armor\\necromancer\\boots.png", "textures\\ptd\\dbb\\attachables\\armor\\necromancer\\chestplate.png", "textures\\ptd\\dbb\\attachables\\armor\\necromancer\\helmet.png", "textures\\ptd\\dbb\\attachables\\armor\\necromancer\\leggings.png", "textures\\ptd\\dbb\\attachables\\armor\\piglin_champion\\boots.png", "textures\\ptd\\dbb\\attachables\\armor\\piglin_champion\\chestplate.png", "textures\\ptd\\dbb\\attachables\\armor\\piglin_champion\\helmet.png", "textures\\ptd\\dbb\\attachables\\armor\\piglin_champion\\leggings.png", "textures\\ptd\\dbb\\attachables\\armor\\void_hydra\\boots.png", "textures\\ptd\\dbb\\attachables\\armor\\void_hydra\\chestplate.png", "textures\\ptd\\dbb\\attachables\\armor\\void_hydra\\helmet.png", "textures\\ptd\\dbb\\attachables\\armor\\void_hydra\\leggings.png", "textures\\ptd\\dbb\\attachables\\armor\\wardzilla\\boots.png", "textures\\ptd\\dbb\\attachables\\armor\\wardzilla\\chestplate.png", "textures\\ptd\\dbb\\attachables\\armor\\wardzilla\\helmet.png", "textures\\ptd\\dbb\\attachables\\armor\\wardzilla\\leggings.png", "textures\\ptd\\dbb\\attachables\\items\\necromancer\\soul_book.png", "textures\\ptd\\dbb\\attachables\\items\\piglin_champion\\axe.png", "textures\\ptd\\dbb\\attachables\\items\\piglin_champion\\goblet.png", "textures\\ptd\\dbb\\attachables\\items\\void_hydra\\void_scepter.png", "textures\\ptd\\dbb\\bosses\\grimhowl\\grimhowl.png", "textures\\ptd\\dbb\\bosses\\grimhowl\\grimhowl_enraged.png", "textures\\ptd\\dbb\\bosses\\grimhowl\\minions\\spirit_wolf.png", "textures\\ptd\\dbb\\bosses\\wardzilla\\default.png", "textures\\ptd\\dbb\\entity\\bosses\\necromancer\\cataclysm.png", "textures\\ptd\\dbb\\entity\\bosses\\necromancer\\default.png", "textures\\ptd\\dbb\\entity\\bosses\\necromancer\\minions\\skeleton_soul.png", "textures\\ptd\\dbb\\entity\\bosses\\necromancer\\minions\\winged_zombie.png", "textures\\ptd\\dbb\\entity\\bosses\\necromancer\\minions\\zombie_brute.png", "textures\\ptd\\dbb\\entity\\bosses\\piglin_champion\\default.png", "textures\\ptd\\dbb\\entity\\bosses\\piglin_champion\\minions\\piglin_brute.png", "textures\\ptd\\dbb\\entity\\bosses\\piglin_champion\\minions\\piglin_marauder.png", "textures\\ptd\\dbb\\entity\\bosses\\piglin_champion\\rock.png", "textures\\ptd\\dbb\\entity\\bosses\\void_hydra\\default.png", "textures\\ptd\\dbb\\entity\\bosses\\void_hydra\\heads\\right_head.png", "textures\\ptd\\dbb\\entity\\projectiles\\flying_skull.png", "textures\\ptd\\dbb\\guidebook\\book_cover.png", "textures\\ptd\\dbb\\items\\grim_howl\\armor\\boots.png", "textures\\ptd\\dbb\\items\\grim_howl\\armor\\chestplate.png", "textures\\ptd\\dbb\\items\\grim_howl\\armor\\helmet.png", "textures\\ptd\\dbb\\items\\grim_howl\\armor\\leggings.png", "textures\\ptd\\dbb\\items\\grim_howl\\bone_sword.png", "textures\\ptd\\dbb\\items\\grim_howl\\essence.png", "textures\\ptd\\dbb\\items\\grim_howl\\spawn_egg.png", "textures\\ptd\\dbb\\items\\necromancer\\armor\\boots.png", "textures\\ptd\\dbb\\items\\necromancer\\armor\\chestplate.png", "textures\\ptd\\dbb\\items\\necromancer\\armor\\helmet.png", "textures\\ptd\\dbb\\items\\necromancer\\armor\\leggings.png", "textures\\ptd\\dbb\\items\\necromancer\\essence.png", "textures\\ptd\\dbb\\items\\necromancer\\spawn_egg.png", "textures\\ptd\\dbb\\items\\piglin_champion\\armor\\boots.png", "textures\\ptd\\dbb\\items\\piglin_champion\\armor\\chestplate.png", "textures\\ptd\\dbb\\items\\piglin_champion\\armor\\helmet.png", "textures\\ptd\\dbb\\items\\piglin_champion\\armor\\leggings.png", "textures\\ptd\\dbb\\items\\piglin_champion\\axe.png", "textures\\ptd\\dbb\\items\\piglin_champion\\essence.png", "textures\\ptd\\dbb\\items\\piglin_champion\\goblet.png", "textures\\ptd\\dbb\\items\\piglin_champion\\spawn_egg.png", "textures\\ptd\\dbb\\items\\reinforced_bottle.png", "textures\\ptd\\dbb\\items\\void_hydra\\armor\\boots.png", "textures\\ptd\\dbb\\items\\void_hydra\\armor\\chestplate.png", "textures\\ptd\\dbb\\items\\void_hydra\\armor\\helmet.png", "textures\\ptd\\dbb\\items\\void_hydra\\armor\\leggings.png", "textures\\ptd\\dbb\\items\\void_hydra\\essence.png", "textures\\ptd\\dbb\\items\\void_hydra\\spawn_egg.png", "textures\\ptd\\dbb\\items\\wardzilla\\armor\\boots.png", "textures\\ptd\\dbb\\items\\wardzilla\\armor\\chestplate.png", "textures\\ptd\\dbb\\items\\wardzilla\\armor\\helmet.png", "textures\\ptd\\dbb\\items\\wardzilla\\armor\\leggings.png", "textures\\ptd\\dbb\\items\\wardzilla\\essence.png", "textures\\ptd\\dbb\\items\\wardzilla\\spawn_egg.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\bubble.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\circle_01.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\circle_02.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\circle_03.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\circle_04.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\dot.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\glow_effect.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\glow_effect1.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\glow_effect2.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\glowdotog.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\glowdotv2.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\glowdotv6.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\rock_01.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\rock_03.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\slash_01.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\smoke_06.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\star_01.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\star_04.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\star_07.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\triangle_01.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\triangle_03.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\triangle_04.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\triangle_07.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\twirl_03.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\bubble.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\circle_01.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\circle_03.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\circle_04.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\dot.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\flip_musicnote_01.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\flip_musicnote_02.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\glow_effect.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\glow_effect1.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\glow_effect2.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\glowdotog.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\glowdotv2.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\glowdotv6.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\glyph_stunned.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\rock_01.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\rock_03.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\slash_01.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\smoke_06.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\star_01.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\star_04.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\star_07.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\triangle_01.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\triangle_03.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\triangle_04.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\triangle_07.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\twirl_03.png", "textures\\ptd\\dbb\\particles\\bubble.png", "textures\\terrain_texture.json", "textures\\texture_list.json"], "build/Dr. Berry's Bosses_rp/": ["animation_controllers\\ptd\\dbb\\attachables\\piglin_champion\\axe.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\grimhowl.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\necromancer\\general.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\necromancer\\look_at_target.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\necromancer\\minions\\s_soul.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\necromancer\\minions\\w_zombie.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\necromancer\\minions\\zombie_brute.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\necromancer\\soul_trap.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\piglin_champion\\general.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\piglin_champion\\look_at_target.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\piglin_champion\\minions\\brute.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\piglin_champion\\minions\\marauder.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\piglin_champion\\stun.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\void_hydra\\attack.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\void_hydra\\general.ac.rp.json", "animation_controllers\\ptd\\dbb\\bosses\\void_hydra\\heads\\right_head.ac.rp.json", "animation_controllers\\ptd\\dbb\\others\\piglin_champion_minion.ac.rp.json", "animations\\ptd\\dbb\\attachables\\piglin_champion\\axe.anim.rp.json", "animations\\ptd\\dbb\\attachables\\piglin_champion\\goblet.anim.rp.json", "animations\\ptd\\dbb\\bosses\\grimhowl\\grimhowl.anim.rp.json", "animations\\ptd\\dbb\\bosses\\grimhowl\\minions\\spirit_wolf.anim.rp.json", "animations\\ptd\\dbb\\bosses\\grimhowl\\wolf_boss.anim.rp.json", "animations\\ptd\\dbb\\bosses\\necromancer\\cataclysm.anim.rp.json", "animations\\ptd\\dbb\\bosses\\necromancer\\minions\\winged_zombie.anim.rp.json", "animations\\ptd\\dbb\\bosses\\necromancer\\minions\\zombie_brute.anim.rp.json", "animations\\ptd\\dbb\\bosses\\necromancer\\necromancer.anim.rp.json", "animations\\ptd\\dbb\\bosses\\necromancer\\skeleton_soul.anim.rp.json", "animations\\ptd\\dbb\\bosses\\necromancer\\soul_trap.anim.rp.json", "animations\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_brute.anim.rp.json", "animations\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_marauder.anim.rp.json", "animations\\ptd\\dbb\\bosses\\piglin_champion\\piglin_champion.anim.rp.json", "animations\\ptd\\dbb\\bosses\\piglin_champion\\rock.anim.rp.json", "animations\\ptd\\dbb\\bosses\\void_hydra\\heads\\right_head.anim.rp.json", "animations\\ptd\\dbb\\bosses\\void_hydra\\void_hydra.anim.rp.json", "animations\\ptd\\dbb\\bosses\\wardzilla\\wardzilla.anim.rp.json", "animations\\ptd\\dbb\\guidebook\\guidebook.anim.rp.json", "animations\\ptd\\dbb\\projectiles\\flying_skull.anim.rp.json", "animations\\ptd\\dbb\\projectiles\\general.anim.rp.json", "attachables\\ptd\\dbb\\grim_howl\\armor\\boots.attachable.json", "attachables\\ptd\\dbb\\grim_howl\\armor\\chestplate.attachable.json", "attachables\\ptd\\dbb\\grim_howl\\armor\\helmet.attachable.json", "attachables\\ptd\\dbb\\grim_howl\\armor\\leggings.attachable.json", "attachables\\ptd\\dbb\\necromancer\\armor\\boots.attachable.json", "attachables\\ptd\\dbb\\necromancer\\armor\\chestplate.attachable.json", "attachables\\ptd\\dbb\\necromancer\\armor\\helmet.attachable.json", "attachables\\ptd\\dbb\\necromancer\\armor\\leggings.attachable.json", "attachables\\ptd\\dbb\\piglin_champion\\armor\\boots.attachable.json", "attachables\\ptd\\dbb\\piglin_champion\\armor\\chestplate.attachable.json", "attachables\\ptd\\dbb\\piglin_champion\\armor\\helmet.attachable.json", "attachables\\ptd\\dbb\\piglin_champion\\armor\\leggings.attachable.json", "attachables\\ptd\\dbb\\piglin_champion\\items\\axe.attachable.json", "attachables\\ptd\\dbb\\piglin_champion\\items\\goblet.attachable.json", "attachables\\ptd\\dbb\\void_hydra\\armor\\boots.attachable.json", "attachables\\ptd\\dbb\\void_hydra\\armor\\chestplate.attachable.json", "attachables\\ptd\\dbb\\void_hydra\\armor\\helmet.attachable.json", "attachables\\ptd\\dbb\\void_hydra\\armor\\leggings.attachable.json", "attachables\\ptd\\dbb\\wardzilla\\armor\\boots.attachable.json", "attachables\\ptd\\dbb\\wardzilla\\armor\\chestplate.attachable.json", "attachables\\ptd\\dbb\\wardzilla\\armor\\helmet.attachable.json", "attachables\\ptd\\dbb\\wardzilla\\armor\\leggings.attachable.json", "entity\\ptd\\dbb\\bosses\\grimhowl.e.rp.json", "entity\\ptd\\dbb\\bosses\\necromancer\\minions\\skeleton_soul.e.rp.json", "entity\\ptd\\dbb\\bosses\\necromancer\\minions\\winged_zombie.e.rp.json", "entity\\ptd\\dbb\\bosses\\necromancer\\minions\\zombie_brute.e.rp.json", "entity\\ptd\\dbb\\bosses\\necromancer\\necromancer.e.rp.json", "entity\\ptd\\dbb\\bosses\\necromancer\\soul_trap.e.rp.json", "entity\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_brute.e.rp.json", "entity\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_marauder.e.rp.json", "entity\\ptd\\dbb\\bosses\\piglin_champion\\piglin_champion.e.rp.json", "entity\\ptd\\dbb\\bosses\\piglin_champion\\rock.e.rp.json", "entity\\ptd\\dbb\\bosses\\void_hydra\\heads\\right_head.e.rp.json", "entity\\ptd\\dbb\\bosses\\void_hydra\\void_hydra.e.rp.json", "entity\\ptd\\dbb\\projectiles\\flying_skull.e.rp.json", "manifest.json", "materials\\entity.material", "models\\entity\\attachables\\grim_howl\\armor\\boots.geo.json", "models\\entity\\attachables\\grim_howl\\armor\\chestplate.geo.json", "models\\entity\\attachables\\grim_howl\\armor\\helmet.geo.json", "models\\entity\\attachables\\grim_howl\\armor\\leggings.geo.json", "models\\entity\\attachables\\necromancer\\armor\\boots.geo.json", "models\\entity\\attachables\\necromancer\\armor\\chestplate.geo.json", "models\\entity\\attachables\\necromancer\\armor\\helmet.geo.json", "models\\entity\\attachables\\necromancer\\armor\\leggings.geo.json", "models\\entity\\attachables\\necromancer\\items\\soul_book.geo.json", "models\\entity\\attachables\\piglin_champion\\armor\\boots.geo.json", "models\\entity\\attachables\\piglin_champion\\armor\\chestplate.geo.json", "models\\entity\\attachables\\piglin_champion\\armor\\helmet.geo.json", "models\\entity\\attachables\\piglin_champion\\armor\\leggings.geo.json", "models\\entity\\attachables\\piglin_champion\\items\\axe.geo.json", "models\\entity\\attachables\\piglin_champion\\items\\goblet.geo.json", "models\\entity\\attachables\\void_hydra\\armor\\boots.geo.json", "models\\entity\\attachables\\void_hydra\\armor\\chestplate.geo.json", "models\\entity\\attachables\\void_hydra\\armor\\helmet.geo.json", "models\\entity\\attachables\\void_hydra\\armor\\leggings.geo.json", "models\\entity\\attachables\\void_hydra\\items\\void_scepter.geo.json", "models\\entity\\attachables\\wardzilla\\armor\\boots.geo.json", "models\\entity\\attachables\\wardzilla\\armor\\chestplate.geo.json", "models\\entity\\attachables\\wardzilla\\armor\\helmet.geo.json", "models\\entity\\attachables\\wardzilla\\armor\\leggings.geo.json", "models\\entity\\bosses\\grimhowl\\grimhowl.geo.json", "models\\entity\\bosses\\grimhowl\\grimhowl_fx.geo.json", "models\\entity\\bosses\\grimhowl\\minions\\spirit_wolf.geo.json", "models\\entity\\bosses\\necromancer\\cataclysm.geo.json", "models\\entity\\bosses\\necromancer\\minions\\winged_zombie.geo.json", "models\\entity\\bosses\\necromancer\\minions\\zombie_brute.geo.json", "models\\entity\\bosses\\necromancer\\necromancer.geo.json", "models\\entity\\bosses\\necromancer\\skeleton_soul.geo.json", "models\\entity\\bosses\\necromancer\\soul_trap.geo.json", "models\\entity\\bosses\\piglin_champion\\minions\\piglin_brute.geo.json", "models\\entity\\bosses\\piglin_champion\\minions\\piglin_marauder.geo.json", "models\\entity\\bosses\\piglin_champion\\piglin_champion.geo.json", "models\\entity\\bosses\\piglin_champion\\rock.geo.json", "models\\entity\\bosses\\void_hydra\\heads\\right_head.geo.json", "models\\entity\\bosses\\void_hydra\\void_hydra.geo.json", "models\\entity\\bosses\\wardzilla\\wardzilla.geo.json", "models\\entity\\bosses\\wardzilla\\weapons\\sculk_slammer.geo.json", "models\\entity\\bosses\\wardzilla\\weapons\\sculk_slammer.png", "models\\entity\\guidebook.geo.json", "models\\entity\\projectiles\\flying_skull.geo.json", "pack_icon.png", "particles\\ptd\\dbb\\necromaner\\nec_arcane1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_arcane1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_arcane2_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_arcane2_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_arcane2_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_arcane3_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_book1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_book1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_book1_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_castinghand1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_castinghand1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_cata1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_cata1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_cata1_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_cata1_04.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_die1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_die1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_die1_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_head1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_head4_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_idle1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_idle1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_idle1_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_phantom1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_phantom1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_phantom1_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_phantom2_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_phantom2_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_phantom2_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_souldrain1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_souldrain1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_souldrain1_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_souldrain1_04.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_souldrain1_05.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_souldrain1_06.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_souldrain2_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_soulhands1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_soulhands1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_soulhands1_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_soulhands1_04.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_spawn1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_spawn1_02.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_spawn1_03.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_spawn1_04.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_spawn1_05.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_summon1_01.particle.json", "particles\\ptd\\dbb\\necromaner\\nec_summon1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_axe1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_axe2_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_body_slam1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_body_slam1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_body_slam1_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_body_slam1_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_body_slam1_05.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_body_slam1_06.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_burp1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_burp1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_burp1_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_charge1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_charge1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_charge1_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_charge2_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_cup1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die1_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die1_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die1_05.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die1_06.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die2_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die2_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_die2_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_foot_stomp1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_foot_stomp1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_foot_stomp1_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_foot_stomp1_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_foot_stomp1_05.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_foot_stomp1_06.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_heal1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_heal1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_heal1_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_heal2_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_heal3_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_heal3_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_schant1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_05.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_06.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_07.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_08.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_09.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn2_10.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn3_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn3_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn3_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn3_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn3_05.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn3_06.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spawn3_07.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam2_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam2_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam3_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam4_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam4_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam4_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam4_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_spinslam4_05.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_stunned1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_stunned1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_stunned2_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_stunned2_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_stunned3_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon1_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon1_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon2_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon2_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon2_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon2_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon3_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon3_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon3_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_summon3_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_upchuck1_01.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_upchuck1_02.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_upchuck1_03.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_upchuck1_04.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_upchuck1_05.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_upchuck1_06.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_upchuck1_07.particle.json", "particles\\ptd\\dbb\\piglin_champion\\pg_upchuck1_08.particle.json", "render_controllers\\ptd\\dbb\\entity\\default.rc.json", "render_controllers\\ptd\\dbb\\entity\\default_emissive.rc.json", "render_controllers\\ptd\\dbb\\entity\\grimhowl.rc.rp.json", "render_controllers\\ptd\\dbb\\entity\\necromancer.rc.json", "render_controllers\\ptd\\dbb\\entity\\skeleton_soul.rc.json", "render_controllers\\ptd\\dbb\\entity\\void_hydra.rc.json", "render_controllers\\ptd\\dbb\\entity\\winged_zombie.rc.json", "render_controllers\\ptd\\dbb\\entity\\zombie_brute.rc.json", "sounds\\ptd\\dbb\\bosses\\grimhowl\\claw_attack.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\dodge.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\grunt.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\grunt_2.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\idle.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\idle_2.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\idle_3.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\pounce_attack.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\pounce_attack_swordless.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\pre_claw.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\pre_pounce.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\roar.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\spawn.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\sword_swipe.ogg", "sounds\\ptd\\dbb\\bosses\\grimhowl\\sword_swipe_2.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\arcane_blast.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\cataclysm.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\death.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\idle.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\move.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\phantom_phase_end.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\phantom_phase_start.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\soul_drain.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\soul_hands.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\spawn.ogg", "sounds\\ptd\\dbb\\bosses\\necromancer\\undead_summon.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\body_slam.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\charging.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\damaged_to_stunned.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\death.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\foot_stomp.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\healing.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\horizontal_attack.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\idle.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\minion_atk1.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\minion_atk2.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\spawn.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\spin_slam.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\stunned_standing.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\stunned_to_idle.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\summoning_chant.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\upchuck.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\vertical_attack.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\walk1.ogg", "sounds\\ptd\\dbb\\bosses\\piglin_champion\\walk2.ogg", "sounds\\sound_definitions.json", "sounds.json", "texts\\en_US.lang", "texts\\languages.json", "textures\\flipbook_textures.json", "textures\\item_texture.json", "textures\\ptd\\dbb\\attachables\\armor\\grim_howl\\boots.png", "textures\\ptd\\dbb\\attachables\\armor\\grim_howl\\chestplate.png", "textures\\ptd\\dbb\\attachables\\armor\\grim_howl\\helmet.png", "textures\\ptd\\dbb\\attachables\\armor\\grim_howl\\leggings.png", "textures\\ptd\\dbb\\attachables\\armor\\necromancer\\boots.png", "textures\\ptd\\dbb\\attachables\\armor\\necromancer\\chestplate.png", "textures\\ptd\\dbb\\attachables\\armor\\necromancer\\helmet.png", "textures\\ptd\\dbb\\attachables\\armor\\necromancer\\leggings.png", "textures\\ptd\\dbb\\attachables\\armor\\piglin_champion\\boots.png", "textures\\ptd\\dbb\\attachables\\armor\\piglin_champion\\chestplate.png", "textures\\ptd\\dbb\\attachables\\armor\\piglin_champion\\helmet.png", "textures\\ptd\\dbb\\attachables\\armor\\piglin_champion\\leggings.png", "textures\\ptd\\dbb\\attachables\\armor\\void_hydra\\boots.png", "textures\\ptd\\dbb\\attachables\\armor\\void_hydra\\chestplate.png", "textures\\ptd\\dbb\\attachables\\armor\\void_hydra\\helmet.png", "textures\\ptd\\dbb\\attachables\\armor\\void_hydra\\leggings.png", "textures\\ptd\\dbb\\attachables\\armor\\wardzilla\\boots.png", "textures\\ptd\\dbb\\attachables\\armor\\wardzilla\\chestplate.png", "textures\\ptd\\dbb\\attachables\\armor\\wardzilla\\helmet.png", "textures\\ptd\\dbb\\attachables\\armor\\wardzilla\\leggings.png", "textures\\ptd\\dbb\\attachables\\items\\necromancer\\soul_book.png", "textures\\ptd\\dbb\\attachables\\items\\piglin_champion\\axe.png", "textures\\ptd\\dbb\\attachables\\items\\piglin_champion\\goblet.png", "textures\\ptd\\dbb\\attachables\\items\\void_hydra\\void_scepter.png", "textures\\ptd\\dbb\\bosses\\grimhowl\\grimhowl.png", "textures\\ptd\\dbb\\bosses\\grimhowl\\grimhowl_enraged.png", "textures\\ptd\\dbb\\bosses\\grimhowl\\minions\\spirit_wolf.png", "textures\\ptd\\dbb\\bosses\\wardzilla\\default.png", "textures\\ptd\\dbb\\entity\\bosses\\necromancer\\cataclysm.png", "textures\\ptd\\dbb\\entity\\bosses\\necromancer\\default.png", "textures\\ptd\\dbb\\entity\\bosses\\necromancer\\minions\\skeleton_soul.png", "textures\\ptd\\dbb\\entity\\bosses\\necromancer\\minions\\winged_zombie.png", "textures\\ptd\\dbb\\entity\\bosses\\necromancer\\minions\\zombie_brute.png", "textures\\ptd\\dbb\\entity\\bosses\\piglin_champion\\default.png", "textures\\ptd\\dbb\\entity\\bosses\\piglin_champion\\minions\\piglin_brute.png", "textures\\ptd\\dbb\\entity\\bosses\\piglin_champion\\minions\\piglin_marauder.png", "textures\\ptd\\dbb\\entity\\bosses\\piglin_champion\\rock.png", "textures\\ptd\\dbb\\entity\\bosses\\void_hydra\\default.png", "textures\\ptd\\dbb\\entity\\bosses\\void_hydra\\heads\\right_head.png", "textures\\ptd\\dbb\\entity\\projectiles\\flying_skull.png", "textures\\ptd\\dbb\\guidebook\\book_cover.png", "textures\\ptd\\dbb\\items\\grim_howl\\armor\\boots.png", "textures\\ptd\\dbb\\items\\grim_howl\\armor\\chestplate.png", "textures\\ptd\\dbb\\items\\grim_howl\\armor\\helmet.png", "textures\\ptd\\dbb\\items\\grim_howl\\armor\\leggings.png", "textures\\ptd\\dbb\\items\\grim_howl\\bone_sword.png", "textures\\ptd\\dbb\\items\\grim_howl\\essence.png", "textures\\ptd\\dbb\\items\\grim_howl\\spawn_egg.png", "textures\\ptd\\dbb\\items\\necromancer\\armor\\boots.png", "textures\\ptd\\dbb\\items\\necromancer\\armor\\chestplate.png", "textures\\ptd\\dbb\\items\\necromancer\\armor\\helmet.png", "textures\\ptd\\dbb\\items\\necromancer\\armor\\leggings.png", "textures\\ptd\\dbb\\items\\necromancer\\essence.png", "textures\\ptd\\dbb\\items\\necromancer\\spawn_egg.png", "textures\\ptd\\dbb\\items\\piglin_champion\\armor\\boots.png", "textures\\ptd\\dbb\\items\\piglin_champion\\armor\\chestplate.png", "textures\\ptd\\dbb\\items\\piglin_champion\\armor\\helmet.png", "textures\\ptd\\dbb\\items\\piglin_champion\\armor\\leggings.png", "textures\\ptd\\dbb\\items\\piglin_champion\\axe.png", "textures\\ptd\\dbb\\items\\piglin_champion\\essence.png", "textures\\ptd\\dbb\\items\\piglin_champion\\goblet.png", "textures\\ptd\\dbb\\items\\piglin_champion\\spawn_egg.png", "textures\\ptd\\dbb\\items\\reinforced_bottle.png", "textures\\ptd\\dbb\\items\\void_hydra\\armor\\boots.png", "textures\\ptd\\dbb\\items\\void_hydra\\armor\\chestplate.png", "textures\\ptd\\dbb\\items\\void_hydra\\armor\\helmet.png", "textures\\ptd\\dbb\\items\\void_hydra\\armor\\leggings.png", "textures\\ptd\\dbb\\items\\void_hydra\\essence.png", "textures\\ptd\\dbb\\items\\void_hydra\\spawn_egg.png", "textures\\ptd\\dbb\\items\\wardzilla\\armor\\boots.png", "textures\\ptd\\dbb\\items\\wardzilla\\armor\\chestplate.png", "textures\\ptd\\dbb\\items\\wardzilla\\armor\\helmet.png", "textures\\ptd\\dbb\\items\\wardzilla\\armor\\leggings.png", "textures\\ptd\\dbb\\items\\wardzilla\\essence.png", "textures\\ptd\\dbb\\items\\wardzilla\\spawn_egg.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\bubble.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\circle_01.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\circle_02.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\circle_03.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\circle_04.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\dot.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\glow_effect.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\glow_effect1.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\glow_effect2.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\glowdotog.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\glowdotv2.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\glowdotv6.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\rock_01.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\rock_03.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\slash_01.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\smoke_06.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\star_01.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\star_04.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\star_07.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\triangle_01.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\triangle_03.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\triangle_04.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\triangle_07.png", "textures\\ptd\\dbb\\particles\\bosses\\necromancer\\twirl_03.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\bubble.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\circle_01.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\circle_03.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\circle_04.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\dot.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\flip_musicnote_01.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\flip_musicnote_02.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\glow_effect.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\glow_effect1.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\glow_effect2.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\glowdotog.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\glowdotv2.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\glowdotv6.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\glyph_stunned.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\rock_01.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\rock_03.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\slash_01.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\smoke_06.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\star_01.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\star_04.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\star_07.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\triangle_01.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\triangle_03.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\triangle_04.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\triangle_07.png", "textures\\ptd\\dbb\\particles\\bosses\\piglin_champion\\twirl_03.png", "textures\\ptd\\dbb\\particles\\bubble.png", "textures\\terrain_texture.json", "textures\\texture_list.json"]}, "bp": {"C:\\Users\\<USER>\\AppData\\Local\\Packages\\Microsoft.MinecraftUWP_8wekyb3d8bbwe\\LocalState\\games\\com.mojang/development_behavior_packs/Dr. Berry's Bosses_bp": ["animation_controllers\\ptd\\dbb\\bosses\\piglin_champion.ac.bp.json", "animations\\ptd\\dbb\\bosses\\piglin_champion.anim.bp.json", "entities\\ptd\\dbb\\bosses\\grimhowl.e.bp.json", "entities\\ptd\\dbb\\bosses\\necromancer\\minions\\skeleton_soul.e.bp.json", "entities\\ptd\\dbb\\bosses\\necromancer\\minions\\winged_zombie.e.bp.json", "entities\\ptd\\dbb\\bosses\\necromancer\\minions\\zombie_brute.e.bp.json", "entities\\ptd\\dbb\\bosses\\necromancer\\necromancer.e.bp.json", "entities\\ptd\\dbb\\bosses\\necromancer\\soul_trap.e.bp.json", "entities\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_brute.e.bp.json", "entities\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_marauder.e.bp.json", "entities\\ptd\\dbb\\bosses\\piglin_champion\\piglin_champion.e.bp.json", "entities\\ptd\\dbb\\bosses\\piglin_champion\\rock.e.bp.json", "entities\\ptd\\dbb\\bosses\\void_hydra\\heads\\right_head.e.bp.json", "entities\\ptd\\dbb\\bosses\\void_hydra\\void_hydra.e.bp.json", "entities\\ptd\\dbb\\guidebook.entity.bp.json", "entities\\ptd\\dbb\\projectiles\\flying_skull.e.bp.json", "items\\ptd\\dbb\\armor\\grim_howl\\boots.json", "items\\ptd\\dbb\\armor\\grim_howl\\chestplate.json", "items\\ptd\\dbb\\armor\\grim_howl\\helmet.json", "items\\ptd\\dbb\\armor\\grim_howl\\leggings.json", "items\\ptd\\dbb\\armor\\necromancer\\boots.json", "items\\ptd\\dbb\\armor\\necromancer\\chestplate.json", "items\\ptd\\dbb\\armor\\necromancer\\helmet.json", "items\\ptd\\dbb\\armor\\necromancer\\leggings.json", "items\\ptd\\dbb\\armor\\piglin_champion\\boots.json", "items\\ptd\\dbb\\armor\\piglin_champion\\chestplate.json", "items\\ptd\\dbb\\armor\\piglin_champion\\helmet.json", "items\\ptd\\dbb\\armor\\piglin_champion\\leggings.json", "items\\ptd\\dbb\\armor\\void_hydra\\boots.json", "items\\ptd\\dbb\\armor\\void_hydra\\chestplate.json", "items\\ptd\\dbb\\armor\\void_hydra\\helmet.json", "items\\ptd\\dbb\\armor\\void_hydra\\leggings.json", "items\\ptd\\dbb\\armor\\wardzilla\\boots.json", "items\\ptd\\dbb\\armor\\wardzilla\\chestplate.json", "items\\ptd\\dbb\\armor\\wardzilla\\helmet.json", "items\\ptd\\dbb\\armor\\wardzilla\\leggings.json", "items\\ptd\\dbb\\essences\\grim_howl_essence.json", "items\\ptd\\dbb\\essences\\necromancer_essence.json", "items\\ptd\\dbb\\essences\\piglin_champion_essence.json", "items\\ptd\\dbb\\essences\\void_hydra_essence.json", "items\\ptd\\dbb\\essences\\wardzilla_essence.json", "items\\ptd\\dbb\\other\\piglin_champion_goblet.json", "items\\ptd\\dbb\\weapons\\piglin_champion_axe.json", "loot_tables\\empty.json", "loot_tables\\ptd\\dbb\\event11.json", "manifest.json", "pack_icon.png", "scripts\\ptd\\dbb\\bosses\\general_attacks\\attackDamages.js", "scripts\\ptd\\dbb\\bosses\\general_attacks\\shockwave.js", "scripts\\ptd\\dbb\\bosses\\general_attacks\\timingUtils.js", "scripts\\ptd\\dbb\\bosses\\general_effects\\camerashake.js", "scripts\\ptd\\dbb\\bosses\\general_mechanics\\bossSummoner.js", "scripts\\ptd\\dbb\\bosses\\general_mechanics\\cinematicCamera.js", "scripts\\ptd\\dbb\\bosses\\general_mechanics\\deathMechanics.js", "scripts\\ptd\\dbb\\bosses\\general_mechanics\\itemFountain.js", "scripts\\ptd\\dbb\\bosses\\general_mechanics\\targetUtils.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attack_controller.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attack_handler.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\backstep.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\index.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\leftClaw.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\pounce.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\rightClaw.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\roar.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\shadowOnslaught.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\slash.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\spinningSlash.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\constants.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\entity_ai.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\index.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\soundManager.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\abilities\\phantomPhase.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\attackTracker.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\attacks\\arcaneBlast.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\attacks\\bookOfTheDamned.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\attacks\\cataclysm.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\attacks\\soulDrain.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\attacks\\soulHands.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\attacks\\soulTrap.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\attacks\\undeadSummon.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\controller.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\flyingSkull.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\index.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\minions\\zombie_brute\\attacks\\splash.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\minions\\zombie_brute\\index.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\soundManager.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\abilities\\healing.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attackTracker.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\body_slam.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\charging.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\foot_stomp.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\horizontal.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\spin_slam.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\stunned_sitting.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\stunned_standing.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\summoning_chant.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\upchuck.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\vertical.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\controller.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\index.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_brute\\attacks\\horizontal.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_brute\\attacks\\vertical.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_brute\\index.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_marauder\\attacks\\slam.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_marauder\\attacks\\sweep.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_marauder\\index.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\soundManager.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\left\\attacks\\leftAtomic.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\left\\attacks\\leftAtomicCross.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\left\\attacks\\leftMissile.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\left\\attacks\\leftRailgun.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\left\\attacks\\leftShout.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\mid\\attacks\\midAtomic.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\mid\\attacks\\midMeteor.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\mid\\attacks\\midSingularity.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\right\\attackTracker.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\right\\attacks\\rightAtomic.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\right\\attacks\\rightAtomicCross.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\right\\attacks\\rightSummon.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\right\\attacks\\rightVacuum.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\right\\controller.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\right\\index.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\soundManager.js", "scripts\\ptd\\dbb\\entities\\index.js", "scripts\\ptd\\dbb\\entities\\lootMechanics.js", "scripts\\ptd\\dbb\\entities\\projectile.js", "scripts\\ptd\\dbb\\entities\\projectileRotation.js", "scripts\\ptd\\dbb\\items\\damageItemstack.js", "scripts\\ptd\\dbb\\items\\index.js", "scripts\\ptd\\dbb\\items\\other\\piglinChampionGoblet.js", "scripts\\ptd\\dbb\\items\\weapons\\piglinChampionAxe.js", "scripts\\ptd\\dbb\\main.js", "scripts\\ptd\\dbb\\player\\index.js", "scripts\\ptd\\dbb\\utilities\\constants\\enchantConstants.js", "scripts\\ptd\\dbb\\utilities\\constants\\enchantableConstants.js", "scripts\\ptd\\dbb\\utilities\\constants\\nonSolidBlocks.js", "scripts\\ptd\\dbb\\utilities\\constants\\protectedBlocks.js", "scripts\\ptd\\dbb\\utilities\\entityQueries.js", "scripts\\ptd\\dbb\\utilities\\items\\decrementStack.js", "scripts\\ptd\\dbb\\utilities\\raycasts.js", "scripts\\ptd\\dbb\\utilities\\rng.js", "scripts\\ptd\\dbb\\utilities\\rotation.js", "scripts\\ptd\\dbb\\utilities\\summonEntity.js", "scripts\\ptd\\dbb\\utilities\\teleport.js", "scripts\\ptd\\dbb\\utilities\\vector3.js", "structures\\ptd\\dbb\\boss_structures\\hobbit.mcstructure", "texts\\en_US.lang", "texts\\languages.json", "trading\\ptd\\dbb\\armorer_trades.json"], "build/Dr. Berry's Bosses_bp/": ["animation_controllers\\ptd\\dbb\\bosses\\piglin_champion.ac.bp.json", "animations\\ptd\\dbb\\bosses\\piglin_champion.anim.bp.json", "entities\\ptd\\dbb\\bosses\\grimhowl.e.bp.json", "entities\\ptd\\dbb\\bosses\\necromancer\\minions\\skeleton_soul.e.bp.json", "entities\\ptd\\dbb\\bosses\\necromancer\\minions\\winged_zombie.e.bp.json", "entities\\ptd\\dbb\\bosses\\necromancer\\minions\\zombie_brute.e.bp.json", "entities\\ptd\\dbb\\bosses\\necromancer\\necromancer.e.bp.json", "entities\\ptd\\dbb\\bosses\\necromancer\\soul_trap.e.bp.json", "entities\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_brute.e.bp.json", "entities\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_marauder.e.bp.json", "entities\\ptd\\dbb\\bosses\\piglin_champion\\piglin_champion.e.bp.json", "entities\\ptd\\dbb\\bosses\\piglin_champion\\rock.e.bp.json", "entities\\ptd\\dbb\\bosses\\void_hydra\\heads\\right_head.e.bp.json", "entities\\ptd\\dbb\\bosses\\void_hydra\\void_hydra.e.bp.json", "entities\\ptd\\dbb\\guidebook.entity.bp.json", "entities\\ptd\\dbb\\projectiles\\flying_skull.e.bp.json", "items\\ptd\\dbb\\armor\\grim_howl\\boots.json", "items\\ptd\\dbb\\armor\\grim_howl\\chestplate.json", "items\\ptd\\dbb\\armor\\grim_howl\\helmet.json", "items\\ptd\\dbb\\armor\\grim_howl\\leggings.json", "items\\ptd\\dbb\\armor\\necromancer\\boots.json", "items\\ptd\\dbb\\armor\\necromancer\\chestplate.json", "items\\ptd\\dbb\\armor\\necromancer\\helmet.json", "items\\ptd\\dbb\\armor\\necromancer\\leggings.json", "items\\ptd\\dbb\\armor\\piglin_champion\\boots.json", "items\\ptd\\dbb\\armor\\piglin_champion\\chestplate.json", "items\\ptd\\dbb\\armor\\piglin_champion\\helmet.json", "items\\ptd\\dbb\\armor\\piglin_champion\\leggings.json", "items\\ptd\\dbb\\armor\\void_hydra\\boots.json", "items\\ptd\\dbb\\armor\\void_hydra\\chestplate.json", "items\\ptd\\dbb\\armor\\void_hydra\\helmet.json", "items\\ptd\\dbb\\armor\\void_hydra\\leggings.json", "items\\ptd\\dbb\\armor\\wardzilla\\boots.json", "items\\ptd\\dbb\\armor\\wardzilla\\chestplate.json", "items\\ptd\\dbb\\armor\\wardzilla\\helmet.json", "items\\ptd\\dbb\\armor\\wardzilla\\leggings.json", "items\\ptd\\dbb\\essences\\grim_howl_essence.json", "items\\ptd\\dbb\\essences\\necromancer_essence.json", "items\\ptd\\dbb\\essences\\piglin_champion_essence.json", "items\\ptd\\dbb\\essences\\void_hydra_essence.json", "items\\ptd\\dbb\\essences\\wardzilla_essence.json", "items\\ptd\\dbb\\other\\piglin_champion_goblet.json", "items\\ptd\\dbb\\weapons\\piglin_champion_axe.json", "loot_tables\\empty.json", "loot_tables\\ptd\\dbb\\event11.json", "manifest.json", "pack_icon.png", "scripts\\ptd\\dbb\\bosses\\general_attacks\\attackDamages.js", "scripts\\ptd\\dbb\\bosses\\general_attacks\\shockwave.js", "scripts\\ptd\\dbb\\bosses\\general_attacks\\timingUtils.js", "scripts\\ptd\\dbb\\bosses\\general_effects\\camerashake.js", "scripts\\ptd\\dbb\\bosses\\general_mechanics\\bossSummoner.js", "scripts\\ptd\\dbb\\bosses\\general_mechanics\\cinematicCamera.js", "scripts\\ptd\\dbb\\bosses\\general_mechanics\\deathMechanics.js", "scripts\\ptd\\dbb\\bosses\\general_mechanics\\itemFountain.js", "scripts\\ptd\\dbb\\bosses\\general_mechanics\\targetUtils.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attack_controller.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attack_handler.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\backstep.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\index.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\leftClaw.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\pounce.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\rightClaw.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\roar.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\shadowOnslaught.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\slash.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\attacks\\spinningSlash.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\constants.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\entity_ai.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\index.js", "scripts\\ptd\\dbb\\bosses\\grimhowl\\soundManager.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\abilities\\phantomPhase.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\attackTracker.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\attacks\\arcaneBlast.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\attacks\\bookOfTheDamned.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\attacks\\cataclysm.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\attacks\\soulDrain.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\attacks\\soulHands.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\attacks\\soulTrap.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\attacks\\undeadSummon.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\controller.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\flyingSkull.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\index.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\minions\\zombie_brute\\attacks\\splash.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\minions\\zombie_brute\\index.js", "scripts\\ptd\\dbb\\bosses\\necromancer\\soundManager.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\abilities\\healing.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attackTracker.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\body_slam.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\charging.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\foot_stomp.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\horizontal.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\spin_slam.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\stunned_sitting.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\stunned_standing.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\summoning_chant.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\upchuck.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\attacks\\vertical.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\controller.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\index.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_brute\\attacks\\horizontal.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_brute\\attacks\\vertical.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_brute\\index.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_marauder\\attacks\\slam.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_marauder\\attacks\\sweep.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\minions\\piglin_marauder\\index.js", "scripts\\ptd\\dbb\\bosses\\piglin_champion\\soundManager.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\left\\attacks\\leftAtomic.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\left\\attacks\\leftAtomicCross.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\left\\attacks\\leftMissile.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\left\\attacks\\leftRailgun.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\left\\attacks\\leftShout.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\mid\\attacks\\midAtomic.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\mid\\attacks\\midMeteor.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\mid\\attacks\\midSingularity.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\right\\attackTracker.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\right\\attacks\\rightAtomic.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\right\\attacks\\rightAtomicCross.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\right\\attacks\\rightSummon.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\right\\attacks\\rightVacuum.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\right\\controller.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\heads\\right\\index.js", "scripts\\ptd\\dbb\\bosses\\void_hydra\\soundManager.js", "scripts\\ptd\\dbb\\entities\\index.js", "scripts\\ptd\\dbb\\entities\\lootMechanics.js", "scripts\\ptd\\dbb\\entities\\projectile.js", "scripts\\ptd\\dbb\\entities\\projectileRotation.js", "scripts\\ptd\\dbb\\items\\damageItemstack.js", "scripts\\ptd\\dbb\\items\\index.js", "scripts\\ptd\\dbb\\items\\other\\piglinChampionGoblet.js", "scripts\\ptd\\dbb\\items\\weapons\\piglinChampionAxe.js", "scripts\\ptd\\dbb\\main.js", "scripts\\ptd\\dbb\\player\\index.js", "scripts\\ptd\\dbb\\utilities\\constants\\enchantConstants.js", "scripts\\ptd\\dbb\\utilities\\constants\\enchantableConstants.js", "scripts\\ptd\\dbb\\utilities\\constants\\nonSolidBlocks.js", "scripts\\ptd\\dbb\\utilities\\constants\\protectedBlocks.js", "scripts\\ptd\\dbb\\utilities\\entityQueries.js", "scripts\\ptd\\dbb\\utilities\\items\\decrementStack.js", "scripts\\ptd\\dbb\\utilities\\raycasts.js", "scripts\\ptd\\dbb\\utilities\\rng.js", "scripts\\ptd\\dbb\\utilities\\rotation.js", "scripts\\ptd\\dbb\\utilities\\summonEntity.js", "scripts\\ptd\\dbb\\utilities\\teleport.js", "scripts\\ptd\\dbb\\utilities\\vector3.js", "structures\\ptd\\dbb\\boss_structures\\hobbit.mcstructure", "texts\\en_US.lang", "texts\\languages.json", "trading\\ptd\\dbb\\armorer_trades.json"]}}