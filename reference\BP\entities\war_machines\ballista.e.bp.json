{"format_version": "1.21.20", "minecraft:entity": {"description": {"identifier": "va:ballista", "is_spawnable": true, "is_summonable": true, "is_experimental": false, "properties": {"va:loading": {"type": "bool", "client_sync": true, "default": false}, "va:loaded": {"type": "bool", "client_sync": true, "default": false}, "va:firing": {"type": "bool", "client_sync": true, "default": false}, "va:controlled": {"type": "bool", "client_sync": true, "default": false}}}, "component_groups": {"va:load": {"minecraft:timer": {"time": 1, "looping": false, "time_down_event": {"event": "va:loaded", "target": "self"}}}}, "events": {"va:load": {"sequence": [{"add": {"component_groups": ["va:load"]}}, {"set_property": {"va:loading": true}}]}, "va:loaded": {"sequence": [{"remove": {"component_groups": ["va:load"]}}, {"set_property": {"va:loading": false}}, {"set_property": {"va:loaded": true}}, {"set_property": {"va:controlled": true}}]}, "va:fire": {"sequence": [{"set_property": {"va:firing": true}}, {"set_property": {"va:loaded": false}}]}, "va:control": {"set_property": {"va:controlled": true}}}, "components": {"minecraft:collision_box": {"width": 1, "height": 1.8}, "minecraft:type_family": {"family": ["war_machine", "ballista"]}, "minecraft:interact": {"interactions": [{"interact_text": "action.hint.war_machine.load", "swing": true, "take_item": true, "on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "va:iron_spear"}, {"test": "bool_property", "subject": "self", "domain": "va:loading", "value": false}, {"test": "bool_property", "subject": "self", "domain": "va:loaded", "value": false}]}, "event": "va:load", "target": "self"}}, {"interact_text": "action.hint.war_machine.control", "swing": true, "take_item": true, "on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "bool_property", "subject": "self", "domain": "va:controlled", "value": false}, {"test": "bool_property", "subject": "self", "domain": "va:loading", "value": false}, {"test": "bool_property", "subject": "self", "domain": "va:loaded", "value": true}]}, "event": "va:control", "target": "self"}}]}, "minecraft:hurt_on_condition": {"damage_conditions": [{"cause": "lava", "damage_per_tick": 4, "filters": {"operator": "==", "subject": "self", "test": "in_lava", "value": true}}]}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": true}, "minecraft:knockback_resistance": {"value": 1}, "minecraft:conditional_bandwidth_optimization": {"default_values": {"max_optimized_distance": 80.0, "max_dropped_ticks": 7, "use_motion_prediction_hints": true}}}}}