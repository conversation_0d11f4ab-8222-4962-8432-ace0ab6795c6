{"format_version": "1.21.20", "minecraft:entity": {"description": {"identifier": "va:thrown_wooden_spear", "is_spawnable": false, "is_summonable": true, "is_experimental": false, "runtime_identifier": "minecraft:snowball", "properties": {"va:hit": {"type": "bool", "client_sync": false, "default": false}, "va:dropped": {"type": "bool", "client_sync": false, "default": false}, "va:durability": {"type": "int", "client_sync": false, "default": 0, "range": [0, 250]}}}, "events": {"va:drop": {"set_property": {"va:dropped": true}}, "va:hit_block": {"sequence": [{"filters": {"test": "bool_property", "subject": "self", "domain": "va:hit", "value": false}, "sequence": [{"trigger": "va:hit_once"}, {"set_property": {"va:hit": true}}]}]}, "va:hit_entity": {"sequence": [{"filters": {"test": "bool_property", "subject": "self", "domain": "va:hit", "value": false}, "sequence": [{"trigger": "va:hit_once"}, {"set_property": {"va:hit": true}}]}]}, "va:hit_once": {}}, "components": {"minecraft:collision_box": {"width": 0.25, "height": 0.35}, "minecraft:type_family": {"family": ["throwable", "spear", "wooden_spear"]}, "minecraft:projectile": {"on_hit": {"impact_damage": {"damage": 3, "knockback": true, "semi_random_diff_damage": false, "destroy_on_hit": false}, "stick_in_ground": {"shake_time": 0.5}}, "destroy_on_hurt": false, "shoot_sound": "item.trident.throw", "liquid_inertia": 0.99, "hit_sound": "item.trident.hit", "hit_ground_sound": "item.trident.hit_ground", "power": 4, "gravity": 0.1, "uncertainty_base": 1, "uncertainty_multiplier": 0, "stop_on_hurt": true, "anchor": 1, "should_bounce": true, "multiple_targets": false, "offset": [0, -0.1, 0]}, "minecraft:hurt_on_condition": {"damage_conditions": [{"cause": "lava", "damage_per_tick": 4, "filters": {"operator": "==", "subject": "self", "test": "in_lava", "value": true}}]}, "minecraft:entity_sensor": {"relative_range": true, "subsensors": [{"event_filters": {"all_of": [{"test": "bool_property", "subject": "self", "domain": "va:dropped", "value": false}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "on_ground", "subject": "self", "value": true}]}, "event": "va:drop", "minimum_count": 1, "range": [0.7, 0.1]}]}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {"default_values": {"max_optimized_distance": 80.0, "max_dropped_ticks": 7, "use_motion_prediction_hints": true}}}}}