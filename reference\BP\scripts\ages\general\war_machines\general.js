import { system } from "@minecraft/server";
export let controlledMachines = new Map();
export function controlMachine(warMachine) {
    const warMachineLocation = warMachine.location;
    let controllingPlayerId = null;
    let control = system.runInterval(() => {
        const isLoaded = warMachine.getProperty('va:loaded');
        if (isLoaded) {
            const nearbyPlayers = warMachine.dimension.getPlayers({ location: warMachineLocation, maxDistance: 24 });
            nearbyPlayers.forEach((player) => {
                if (controlledMachines.has(player.id)) {
                    if (controllingPlayerId === null) {
                        controllingPlayerId = player.id;
                    }
                    const playerRotation = player.getRotation();
                    const isPlayerSneaking = player.isSneaking;
                    warMachine.setRotation(playerRotation);
                    if (isPlayerSneaking) {
                        warMachine.triggerEvent('va:fire');
                    }
                }
            });
        }
        else {
            system.clearRun(control);
            if (controllingPlayerId !== null) {
                controlledMachines.delete(controllingPlayerId);
            }
        }
    });
    return;
}
export async function fireMachine(warMachine, entityId) {
    const { dimension, location } = warMachine;
    const controllingPlayer = dimension.getPlayers({ location, closest: 1 })[0];
    if (entityId !== 'va:ballista')
        return;
    await system.waitTicks(1);
    warMachine.resetProperty('va:firing');
    dimension.playSound('mob.drowned.shoot', location);
    const rotation = controllingPlayer.getRotation();
    const yaw = rotation.y;
    const pitch = rotation.x;
    const yawRadians = (yaw * Math.PI) / 180;
    const pitchRadians = (pitch * Math.PI) / 180;
    const direction = {
        x: -Math.cos(pitchRadians) * Math.sin(yawRadians),
        y: -Math.sin(pitchRadians),
        z: Math.cos(pitchRadians) * Math.cos(yawRadians),
    };
    const headLocation = warMachine.getHeadLocation();
    const offsetDistance = 2;
    const spawnLocation = {
        x: headLocation.x + direction.x * offsetDistance,
        y: headLocation.y + direction.y * offsetDistance,
        z: headLocation.z + direction.z * offsetDistance,
    };
    const speed = 8;
    const velocity = {
        x: direction.x * speed,
        y: direction.y * speed,
        z: direction.z * speed,
    };
    const projectile = dimension.spawnEntity('va:thrown_iron_spear', spawnLocation);
    const projectileComponent = projectile.getComponent('minecraft:projectile');
    if (projectileComponent) {
        projectileComponent.shoot(velocity);
    }
    else {
        projectile.applyImpulse(velocity);
    }
    return;
}
