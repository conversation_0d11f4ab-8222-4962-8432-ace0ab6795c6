import { system } from "@minecraft/server";
import { spawnEntitiesWithInterval } from "../../../utilities/summonEntity";
import { getRandomLocation, getDistance } from "../../../utilities/vector3";
import { countPiglinChampionMinions } from "../controller";
/**
 * Generates minion configurations for summoning
 * @param count Number of minions to spawn
 * @returns Array of entity configurations for spawning
 */
function generateMinionCombination(count) {
    if (count <= 0) {
        return [];
    }
    // For single minion, use the original logic
    if (count === 1) {
        const combinations = [[{ entityId: "ptd_dbb:piglin_brute", count: 1 }], [{ entityId: "ptd_dbb:piglin_marauder", count: 1 }]];
        const randomIndex = Math.floor(Math.random() * combinations.length);
        return combinations[randomIndex];
    }
    // For multiple minions, create a mix of brutes and marauders
    const configs = [];
    let remainingCount = count;
    // Randomly distribute between brutes and marauders
    while (remainingCount > 0) {
        const entityType = Math.random() < 0.5 ? "ptd_dbb:piglin_brute" : "ptd_dbb:piglin_marauder";
        const spawnCount = Math.min(remainingCount, Math.floor(Math.random() * remainingCount) + 1);
        // Check if we already have this entity type in configs
        const existingConfig = configs.find((config) => config.entityId === entityType);
        if (existingConfig) {
            existingConfig.count += spawnCount;
        }
        else {
            configs.push({ entityId: entityType, count: spawnCount });
        }
        remainingCount -= spawnCount;
    }
    return configs;
}
/**
 * Attack timing in ticks - when the damage should be applied during the animation
 */
const ATTACK_TIMING = 40;
/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 125;
/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;
/**
 * Executes the summoning chant attack for the Piglin Champion using the new timing system
 * Uses localized runTimeout for attack timing, reset, and cooldown
 *
 * @param piglinChampion The piglin champion entity
 * @param target The target entity (optional)
 */
export function executeSummoningChantAttack(piglinChampion, target) {
    // Wait for the attack timing before executing the attack
    let timing = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(timing);
                return;
            }
            performSummoningChantAttack(piglinChampion, target);
        }
        catch (error) {
            system.clearRun(timing);
        }
    }, ATTACK_TIMING);
    // Reset the attack state to "none" after the animation is complete
    let reset = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            if (isDead) {
                system.clearRun(reset);
                return;
            }
            // Only reset if the attack is still "summoning_chant" - prevents interference with stuns
            if (currentAttack === "summoning_chant") {
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
            system.clearRun(reset);
        }
        catch (error) {
            system.clearRun(reset);
        }
    }, ANIMATION_TIME);
    // Wait for cooldown, then set cooldown property to false to select the next attack
    let cooldown = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldown);
                return;
            }
            piglinChampion.setProperty("ptd_dbb:cooling_down", false);
            system.clearRun(cooldown);
        }
        catch (error) {
            system.clearRun(cooldown);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
    return;
}
/**
 * Delay between minion spawns in ticks
 */
const SPAWN_DELAY = 15;
/**
 * Performs the actual summoning chant attack using the original working approach
 * @param piglinChampion The piglin champion entity
 * @param target The target entity (optional)
 */
async function performSummoningChantAttack(piglinChampion, target) {
    // Get the piglin champion's unique ID for tagging minions
    const championId = piglinChampion.id;
    // Count existing minions belonging to this champion
    const existingMinionCount = countPiglinChampionMinions(piglinChampion, 64);
    // Calculate how many minions to spawn (maximum 3 total)
    const minionsToSpawn = Math.max(0, 3 - existingMinionCount);
    // If we already have 3 or more minions, don't spawn any more
    if (minionsToSpawn <= 0) {
        return;
    }
    // Generate minion configurations for the calculated count
    const entityConfigs = generateMinionCombination(minionsToSpawn);
    // Determine spawn location based on target distance
    let spawnAroundTarget = false;
    if (target) {
        try {
            const distance = getDistance(piglinChampion.location, target ? target.location : piglinChampion.location);
            // If target is in unreachable range (12-32 blocks), spawn around target
            spawnAroundTarget = distance >= 12 && distance <= 32;
        }
        catch (e) {
            spawnAroundTarget = false;
        }
    }
    // Spawn the minions with a delay between each using the original working approach
    await spawnEntitiesWithInterval(piglinChampion.dimension, entityConfigs, () => {
        // Choose spawn location based on whether target is in unreachable range
        const baseLocation = spawnAroundTarget && target ? target.location : piglinChampion.location;
        // Use getRandomLocation to get a random position around the chosen entity
        const pos = getRandomLocation(baseLocation, piglinChampion.dimension, 3, // Base offset (minimum distance from chosen entity)
        4, // Additional offset (random extra distance)
        0, // No Y offset
        true // Check for air block
        );
        // If we got a valid position, add visual effects
        if (pos) {
            piglinChampion.dimension.spawnParticle("minecraft:large_explosion", pos);
        }
        return pos;
    }, SPAWN_DELAY, // 15 ticks delay between spawns (0.75 seconds)
    (entity) => {
        // Tag the minion with the piglin champion's ID immediately after spawning
        entity.setDynamicProperty("ptd_dbb:champion_id", championId);
        // Play a sound and visual effect
        piglinChampion.dimension.playSound("random.totem", entity.location, { volume: 200, pitch: 0.9 });
        piglinChampion.dimension.spawnParticle("ptd_dbb:pg_summon2_01", entity.location);
    });
    return;
}
