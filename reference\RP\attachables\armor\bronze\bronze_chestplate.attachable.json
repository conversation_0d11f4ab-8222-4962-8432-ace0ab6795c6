{"format_version": "1.20.50", "minecraft:attachable": {"description": {"identifier": "va:bronze_chestplate", "materials": {"default": "entity_emissive_alpha", "enchanted": "armor_enchanted"}, "textures": {"default": "textures/attachables/armor/bronze/chestplate", "enchanted": "textures/misc/enchanted_item_glint"}, "geometry": {"default": "geometry.bronze_chestplate"}, "scripts": {"parent_setup": "variable.chest_layer_visible = 0.0;"}, "render_controllers": ["controller.render.item_default"]}}}