# Boss Death Mechanics Documentation

## 1. Overview

The Death Mechanics system provides a standardized framework for handling boss death sequences in the Dr. Berry Bosses addon. This system ensures consistent behavior across all bosses while allowing for boss-specific customization.

### 1.1 Purpose and Functionality

The Death Mechanics system serves several key purposes:

- Creates a cinematic death sequence for bosses
- Handles item drops with configurable properties
- Manages XP orb spawning over time
- Controls sound effects during death
- Ensures the boss remains in place during the death animation
- Provides hooks for custom behavior at specific points in the death sequence

### 1.2 Common Elements Across All Bosses

All bosses share these common death mechanics:

- A configurable death animation duration
- Progressive XP orb spawning
- Boss essence drops in a fountain-like effect
- Death sound effects with sound management
- Immobilization during death animation
- Entity removal upon completion

### 1.3 Death Triggering

Boss death is triggered when:

1. The boss's health reaches a threshold (typically 10 or below)
2. The `ptd_bb:dead` property is set to true
3. Any ongoing attacks are canceled
4. The death timer is initialized to 0

## 2. Death Sequence Flow

### 2.1 Initialization (Tick 1)

When death is first triggered:

1. The death location is saved as a dynamic property to prevent movement
2. The death sound is played (after stopping all other boss sounds)
3. Item drops are spawned with configured height offsets
4. The `onStart` callback is executed if provided

### 2.2 Animation and Visual Effects

During the death sequence:

1. The boss's animation controller transitions to the death animation
2. The entity is teleported to the saved death location each tick to prevent movement
3. A slowness effect (amplifier 255) is applied to prevent movement
4. Custom particle effects may be spawned depending on boss-specific configuration

### 2.3 Sound Management

Sound handling during death:

1. All boss-specific sounds are stopped using the provided `stopSoundsFn`
2. The death sound is played once at the beginning of the sequence
3. The `stopsound` command is used to ensure clean sound transitions

### 2.4 Item Drops

Item drops are handled at the beginning of the death sequence:

1. Boss essence items are spawned in a fountain-like effect from the boss's location
2. The essence fountain creates a visually appealing distribution of items
3. A configurable number of essence items (typically 32) are spawned sequentially
4. Two formats are supported for configuration:
   - Legacy format: Simple item ID and count (with optional random range)
   - ItemStack format: Pre-configured ItemStack objects with specific properties

### 2.5 XP Orb Spawning

XP orbs are spawned progressively:

1. A configurable number of XP orbs are spawned each tick
2. Spawning continues for a configurable duration (default: 50 ticks)
3. XP orbs are spawned at the boss's location with a configurable height offset

### 2.6 Entity Removal

At the end of the death sequence:

1. The `onComplete` callback is executed if provided
2. By default, the entity is removed from the world
3. Custom behavior can be implemented through the `onComplete` callback

## 3. Configuration Options

The Death Mechanics system is highly configurable through the `DeathMechanicsOptions` interface.

### 3.1 Duration Settings

- `duration`: Total duration of the death animation in ticks (default: 100)
  - Controls how long the death sequence plays before the entity is removed
  - Typically ranges from 100-200 ticks depending on the boss

### 3.2 XP Orb Configuration

- `xpOrbs.count`: Number of XP orbs to spawn per tick (default: 4)
  - Higher values create a denser stream of XP orbs
  - Typically ranges from 4-8 depending on the boss difficulty

- `xpOrbs.duration`: Duration in ticks to spawn XP orbs (default: 50)
  - Controls how long XP orbs continue to spawn
  - Typically shorter than the total death animation duration

- `xpOrbs.heightOffset`: Height offset for spawning XP orbs (default: 2.25)
  - Adjusts the vertical position of spawned XP orbs
  - Typically set to match the boss's center of mass

### 3.3 Item Drop Configuration

Items can be configured in two formats:

#### Legacy Format
```typescript
{
  item: string,           // Item identifier
  count: number | [number, number], // Fixed count or [min, max] range
  heightOffset?: number   // Height offset (default: 2.25)
}
```

#### ItemStack Format
```typescript
{
  itemStack: ItemStack,   // Pre-configured ItemStack
  heightOffset?: number   // Height offset (default: 2.25)
}
```

For boss essence drops, the `spawnItemFountain` function from the general mechanics module is used to create a fountain-like effect:

```typescript
/**
 * Spawns items in a fountain-like effect
 *
 * @param entity The entity at whose location to spawn the fountain
 * @param itemId The item identifier to spawn
 * @param count The number of items to spawn
 * @param options Configuration options for the fountain effect
 */
export async function spawnItemFountain(
    entity: Entity,
    itemId: string,
    count: number,
    options?: ItemFountainOptions
): Promise<void> {
    // Merge default options with provided options
    const config = { ...DEFAULT_OPTIONS, ...options };

    const dimension = entity.dimension;
    const location = entity.location;

    // Base spawn location
    const spawnLocation: Vector3 = {
        x: location.x,
        y: location.y + (config.heightOffset || 2.25),
        z: location.z
    };

    // Create a new ItemStack for the item
    const itemStack = new ItemStack(itemId, 1);

    // Play sound effect if provided
    if (config.soundEffect) {
        dimension.playSound(config.soundEffect, location);
    }

    // Spawn items with a delay to create a fountain effect
    for (let i = 0; i < count; i++) {
        // Determine delay for this item
        let delay = 1; // Default delay
        if (Array.isArray(config.spawnDelay)) {
            // Use alternating delays if an array is provided
            const index = i % config.spawnDelay.length;
            const delayValue = config.spawnDelay[index];
            if (typeof delayValue === 'number') {
                delay = delayValue;
            }
        } else if (typeof config.spawnDelay === 'number') {
            // Use fixed delay if a single number is provided
            delay = config.spawnDelay;
        }

        // Wait the specified number of ticks between spawns
        await system.waitTicks(delay);

        try {
            // Spawn the item
            const item = dimension.spawnItem(itemStack, spawnLocation);

            if (item) {
                // Apply random impulse for fountain effect
                applyFountainImpulse(item, config);

                // Spawn particle effect if provided
                if (config.particleEffect) {
                    dimension.spawnParticle(config.particleEffect, item.location);
                }
            }
        } catch (error) {
            console.warn(`Error spawning item in fountain: ${error}`);
        }
    }
}
```

### 3.4 Sound Effects

- `deathSound`: Sound to play when death sequence starts
  - Typically a boss-specific death sound
  - Played once at the beginning of the death sequence

- `stopSoundsFn`: Function to stop boss sounds (required parameter)
  - Each boss must provide its own sound stopping function
  - Uses the `stopsound` command for effective sound management

### 3.5 Custom Callbacks and Events

- `onStart`: Callback function when death sequence starts (tick 1)
  - Called after item drops are spawned
  - Can be used for additional initialization

- `onComplete`: Callback function when death sequence completes (final tick)
  - Called before the default entity removal
  - Can override the default removal behavior

- `customEvents`: Array of events at specific ticks
  ```typescript
  {
    tick: number,                    // Tick number when the event should trigger
    callback: (entity: Entity) => void // Callback function to execute
  }
  ```
  - Allows for custom behavior at specific points in the death sequence
  - Can be used for additional particle effects, sounds, or other actions

### 3.6 Other Configuration

- `propertyPrefix`: Property prefix for entity properties (default: "ptd_bb")
  - Used to construct property names like `${prefix}:dead` and `${prefix}:death_timer`
  - Typically left at the default value

## 4. Implementation Details

### 4.1 Entity Properties

The Death Mechanics system uses several entity properties:

- `ptd_bb:dead` (boolean): Whether the entity is dead
  - Set to true when death is triggered
  - Used by animation controllers to transition to death animation

- `ptd_bb:death_timer` (integer): Current tick of the death animation
  - Incremented each tick during the death sequence
  - Used to trigger events at specific points in the sequence

- `ptd_bb:death_location` (dynamic property): Saved location for teleporting
  - Stored as a JSON string: `{"x": x, "y": y, "z": z}`
  - Used to teleport the entity back to its death location each tick

### 4.2 Animation Controllers

Death animations are controlled by animation controllers in the resource pack:

```json
"transitions": [
  {"dead": "q.property('ptd_bb:dead') == true"}
]
```

The animation controller transitions to the death animation when the `ptd_bb:dead` property is true.

### 4.3 Sound Management

Each boss provides a sound stopping function that stops all boss-specific sounds:

```typescript
export function stopPiglinChampionSounds(entity: Entity, excludedSound?: string): void {
  try {
    if (!entity) return;

    // Use the stopsound command to stop all sounds except the excluded one
    for (const sound of PIGLIN_CHAMPION_SOUNDS) {
      // Skip the excluded sound
      if (excludedSound && sound === excludedSound) continue;

      // Use the stopsound command to stop the sound
      try {
        entity.runCommand(`stopsound @a ${sound}`);
      } catch (cmdError) {
        console.warn(`Error running stopsound command for ${sound}: ${cmdError}`);
      }
    }
  } catch (error) {
    console.warn(`Error stopping Piglin Champion sounds: ${error}`);
  }
}
```

### 4.4 Location Handling

The death location is saved and used to prevent movement during the death animation:

1. The location is saved as a JSON string in a dynamic property
2. The entity is teleported to this location each tick
3. A slowness effect (amplifier 255) is applied to prevent movement

### 4.5 Item Drop Handling

Item drops are handled at the beginning of the death sequence:

1. For legacy format drops, the count can be a fixed number or a random range
2. For ItemStack format drops, pre-configured ItemStacks are used
3. Each drop has a configurable height offset

### 4.6 Error Handling

The Death Mechanics system includes robust error handling:

1. Entity validity checks to prevent errors with invalid entities
2. Try-catch blocks around critical operations
3. Fallbacks for JSON parsing errors
4. Graceful exits when errors occur

## 5. Boss-Specific Implementations

### 5.1 Piglin Champion Example

The Piglin Champion configures its death mechanics as follows:

```typescript
if (handleDeathMechanics(piglinChampion, {
  // Configure death mechanics specific to the Piglin Champion
  duration: 100,
  xpOrbs: {
    count: 8,
    duration: 30,
    heightOffset: 2.25
  },
  // No drops here as we'll use a custom event to spawn the essence fountain
  drops: [],
  deathSound: "mob.piglin.death",
  // Add custom event to spawn essence fountain at the beginning of death sequence
  customEvents: [
    {
      tick: 1,
      callback: (entity) => {
        // Spawn 32 essence items in a fountain-like effect
        spawnItemFountain(
          entity,
          "ptd_bb:piglin_champion_essence",
          32,
          {
            heightOffset: 2.25,
            particleEffect: "minecraft:large_explosion",
            soundEffect: "random.pop",
            minVerticalStrength: 0.1,
            maxVerticalStrength: 0.3,
            minHorizontalStrength: 0.05,
            maxHorizontalStrength: 0.2
          }
        );
      }
    }
  ],
  // Provide the sound stopping function
  stopSoundsFn: stopPiglinChampionSounds
})) {
  // If death mechanics were applied, return early
  return;
}
```

### 5.2 Implementing for New Bosses

To implement death mechanics for a new boss:

1. Define entity properties in the entity JSON file:
   ```json
   "ptd_bb:dead": {
     "type": "bool",
     "client_sync": true,
     "default": false
   },
   "ptd_bb:death_timer": {
     "type": "int",
     "client_sync": true,
     "range": [0, 100],
     "default": 0
   }
   ```

2. Create a sound stopping function for the boss:
   ```typescript
   export function stopNewBossSounds(entity: Entity, excludedSound?: string): void {
     // Implementation similar to stopPiglinChampionSounds
   }
   ```

3. Configure death mechanics in the boss's tick function:
   ```typescript
   if (handleDeathMechanics(newBoss, {
     duration: 100,
     xpOrbs: { count: 6, duration: 40 },
     // No drops here as we'll use a custom event to spawn the essence fountain
     drops: [],
     deathSound: "mob.newboss.death",
     // Add custom event to spawn essence fountain at the beginning of death sequence
     customEvents: [
       {
         tick: 1,
         callback: (entity) => {
           // Spawn essence items in a fountain-like effect
           spawnItemFountain(
             entity,
             "ptd_bb:new_boss_essence",
             32,
             {
               heightOffset: 2.25,
               particleEffect: "minecraft:large_explosion",
               soundEffect: "random.pop",
               minVerticalStrength: 0.1,
               maxVerticalStrength: 0.3,
               minHorizontalStrength: 0.05,
               maxHorizontalStrength: 0.2
             }
           );
         }
       }
     ],
     stopSoundsFn: stopNewBossSounds
   })) {
     return;
   }
   ```

4. Add death animation to the boss's animation controller:
   ```json
   "transitions": [
     {"dead": "q.property('ptd_bb:dead') == true"}
   ]
   ```

## 6. Technical Reference

### 6.1 DeathMechanicsOptions Interface

```typescript
export interface DeathMechanicsOptions {
  /**
   * Total duration of death animation in ticks
   * @default 100
   */
  duration?: number;

  /**
   * XP orb spawn configuration
   */
  xpOrbs?: {
    /**
     * Number of XP orbs to spawn per tick
     * @default 4
     */
    count: number;

    /**
     * Duration in ticks to spawn XP orbs
     * @default 50
     */
    duration: number;

    /**
     * Height offset for spawning XP orbs
     * @default 2.25
     */
    heightOffset?: number;
  };

  /**
   * Item drops configuration
   * Can be either an array of item descriptors (legacy format) or an array of ItemStacks
   */
  drops?: Array<{
    /**
     * Item identifier to drop
     */
    item: string;

    /**
     * Number of items to drop
     * Can be a fixed number or a [min, max] range for random amount
     */
    count: number | [number, number];

    /**
     * Height offset for dropping items
     * @default 2.25
     */
    heightOffset?: number;
  } | {
    /**
     * Pre-configured ItemStack to drop
     */
    itemStack: ItemStack;

    /**
     * Height offset for dropping items
     * @default 2.25
     */
    heightOffset?: number;
  }>;

  /**
   * Sound to play when death sequence starts
   */
  deathSound?: string;

  /**
   * Callback function when death sequence starts (tick 1)
   */
  onStart?: (entity: Entity) => void;

  /**
   * Callback function when death sequence completes (final tick)
   * Default behavior is to remove the entity
   */
  onComplete?: (entity: Entity) => void;

  /**
   * Custom events at specific ticks during death sequence
   */
  customEvents?: Array<{
    /**
     * Tick number when the event should trigger
     */
    tick: number;

    /**
     * Callback function to execute
     */
    callback: (entity: Entity) => void;
  }>;

  /**
   * Property prefix for entity properties
   * @default "ptd_bb"
   */
  propertyPrefix?: string;

  /**
   * Function to stop boss sounds
   * Each boss must provide its own sound stopping function
   */
  stopSoundsFn: (entity: Entity, excludedSound?: string) => void;
}
```

### 6.2 Default Configuration Values

```typescript
const DEFAULT_OPTIONS = {
  duration: 100,
  xpOrbs: {
    count: 4,
    duration: 50,
    heightOffset: 2.25
  },
  propertyPrefix: "ptd_bb"
  // Note: stopSoundsFn is required and must be provided by each boss implementation
};
```

### 6.3 Required Parameters

The only strictly required parameter is `stopSoundsFn`, which must be provided by each boss implementation. All other parameters have default values or are optional.

## 7. Best Practices

### 7.1 Sound Management

- Always provide a comprehensive list of all possible boss sounds in the sound stopping function
- Use try-catch blocks around sound commands to handle potential errors
- Test sound stopping with various scenarios to ensure clean transitions

### 7.2 Item Drops

- Use the `spawnItemFountain` function from the general mechanics module for a visually appealing distribution of essence items
- Configure the appropriate number of essence items based on boss difficulty (typically 32)
- Use the customEvents array with a tick 1 event to spawn the essence fountain at the beginning of the death sequence
- Customize the fountain effect with particle effects and sound effects for a more dramatic presentation
- Adjust the impulse strength parameters to control the spread and height of the fountain
- Consider the height offset carefully to ensure the fountain effect is visible and items are accessible
- Use the boss-specific essence item identifier (e.g., `ptd_bb:piglin_champion_essence`)

### 7.3 Performance Considerations

- Limit the number of XP orbs spawned per tick to prevent performance issues
- Use efficient sound stopping methods to prevent audio glitches
- Consider the total duration carefully to balance cinematic effect with gameplay flow

### 7.4 Error Handling

- Always check entity validity before performing operations
- Use try-catch blocks around critical code sections
- Provide fallbacks for potential failure points
- Log errors for debugging purposes