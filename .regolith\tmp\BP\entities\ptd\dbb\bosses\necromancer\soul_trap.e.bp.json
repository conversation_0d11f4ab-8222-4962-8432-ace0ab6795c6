{"format_version": "1.21.70", "minecraft:entity": {"description": {"identifier": "ptd_dbb:soul_trap", "is_spawnable": false, "is_summonable": true, "is_experimental": false}, "component_groups": {"minecraft:despawn": {"minecraft:instant_despawn": {}}}, "events": {"ptd_dbb:despawn": {"add": {"component_groups": ["minecraft:despawn"]}}}, "components": {"minecraft:collision_box": {"width": 0.1, "height": 0.1}, "minecraft:type_family": {"family": ["inanimate", "soul_trap", "necromancer"]}, "minecraft:timer": {"time": 3.9166, "looping": false, "time_down_event": {"event": "ptd_dbb:despawn", "target": "self"}}, "minecraft:damage_sensor": {"triggers": [{"cause": "all", "deals_damage": "no"}]}, "minecraft:knockback_resistance": {"value": 1}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": false}, "minecraft:conditional_bandwidth_optimization": {}}}}