import { EasingType, Entity, Player, Vector3, system } from "@minecraft/server";

export interface CinematicCameraOptions {
    title: string;
    subtitle: string;
    closeUpTimePercentage?: number;
    keyframeCount?: number;
    totalDuration?: number;
    semiCloseUpDistance?: number;
    closeUpDistance?: number;
    radius?: number;
}

export function showcaseBossWithCinematicCamera(
    targetEntity: Entity,
    options: CinematicCameraOptions
): void {
    const {
        title,
        subtitle,
        closeUpTimePercentage = 0.3,
        keyframeCount = 2,
        totalDuration = 7,
        semiCloseUpDistance = 9,
        closeUpDistance = 11,
        radius = 8
    } = options;

    try {
        if (!targetEntity) return;

        const targetHeadLocation: Vector3 = targetEntity.getHeadLocation();
        const targetLocation: Vector3 = targetEntity.location;
        targetEntity.setDynamicProperty('ptd_bb:boss_camera_ongoing', true);

        const totalPercentage: number = 1.0;
        const remainingPercentage: number = totalPercentage - closeUpTimePercentage;

        const normalizationFactor: number = 0.2 + 0.0714 + 0.2142 + 0.5714;
        const titleDuration: number = totalDuration * (remainingPercentage * (0.2 / normalizationFactor));
        const semiCloseUpTime: number = totalDuration * (remainingPercentage * (0.0714 / normalizationFactor));
        const finalCloseUpTime: number = totalDuration * (remainingPercentage * (0.2142 / normalizationFactor));
        const resetTime: number = totalDuration * (remainingPercentage * (0.5714 / normalizationFactor));
        const keyframeTime: number = totalDuration * closeUpTimePercentage;

        const keyframes: (Vector3 & { easeTime: number })[] = [];
        const minDistance: number = 4;

        for (let i = 0; i < keyframeCount; i++) {
            let keyframe: Vector3 & { easeTime: number };
            let isTooClose: boolean;

            do {
                const randomAngle = Math.random() * 2 * Math.PI;
                const randomRadius = radius + Math.random() * 4;
                keyframe = {
                    x: targetLocation.x + randomRadius * Math.cos(randomAngle),
                    y: targetLocation.y + 3 + Math.random() * 2,
                    z: targetLocation.z + randomRadius * Math.sin(randomAngle),
                    easeTime: keyframeTime / (keyframeCount * 2)
                };
                isTooClose = keyframes.some(existingKeyframe => {
                    const dx = keyframe.x - existingKeyframe.x;
                    const dy = keyframe.y - existingKeyframe.y;
                    const dz = keyframe.z - existingKeyframe.z;
                    const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
                    return distance < minDistance;
                });
            } while (isTooClose);

            keyframes.push(keyframe);
        }

        const nearbyPlayers: Player[] = targetEntity.dimension.getPlayers({
            location: targetLocation,
            maxDistance: 64
        });

        const viewDirection = targetEntity.getViewDirection();
        const initialFrontPosition: Vector3 = {
            x: targetLocation.x + viewDirection.x * 8,
            y: targetLocation.y + 3,
            z: targetLocation.z + viewDirection.z * 8
        };

        nearbyPlayers.forEach(player => {
            player.setDynamicProperty('ptd_bb:boss_camera', true);
            player.teleport(player.location, {
                facingLocation: {
                    x: targetHeadLocation.x,
                    y: targetHeadLocation.y - 1,
                    z: targetHeadLocation.z
                }
            });
            player.playSound("mob.phantom.flap", { volume: 1, pitch: 1.5 + (Math.random() * 0.3) });
            player.addEffect('minecraft:invisibility', totalDuration * 20, { amplifier: 255, showParticles: false });
            player.camera.setCamera("minecraft:free", {
                location: initialFrontPosition,
                facingLocation: targetLocation,
                easeOptions: { easeTime: keyframeTime / keyframeCount, easeType: EasingType.InOutSine }
            });
        });

        system.runTimeout(() => {
            let currentKeyframe = 0;

            const interval = system.runInterval(() => {
                if (!targetEntity) {
                    nearbyPlayers.forEach(player => player.camera.clear());
                    return;
                }
                if (currentKeyframe < keyframes.length) {
                    const keyframe = keyframes[currentKeyframe];
                    if (!keyframe) return; // Skip this tick if keyframe is undefined

                    nearbyPlayers.forEach(player => {
                        player.playSound("mob.phantom.flap", { location: { x: keyframe.x, y: keyframe.y, z: keyframe.z }, volume: 1, pitch: 1.5 + (Math.random() * 0.3) });
                        player.camera.setCamera("minecraft:free", {
                            location: { x: keyframe.x, y: keyframe.y, z: keyframe.z },
                            facingLocation: targetLocation,
                            easeOptions: { easeTime: keyframe.easeTime, easeType: EasingType.InOutSine }
                        });
                        system.runTimeout(() => {
                            player.playSound("mob.phantom.flap", { location: { x: keyframe.x - 1, y: keyframe.y - 1, z: keyframe.z - 1 }, volume: 1, pitch: 1.5 + (Math.random() * 0.3) });
                            player.camera.setCamera("minecraft:free", {
                                location: { x: keyframe.x - 1, y: keyframe.y - 1, z: keyframe.z - 1 },
                                facingLocation: targetLocation,
                                easeOptions: { easeTime: 0.5, easeType: EasingType.InOutSine }
                            });
                        }, (keyframeTime / (keyframeCount * 2)) * 20);
                    });

                    currentKeyframe++;
                } else {
                    system.clearRun(interval);

                    const semiCloseUpPosition: Vector3 = {
                        x: targetLocation.x + viewDirection.x * semiCloseUpDistance,
                        y: targetLocation.y + 4,
                        z: targetLocation.z + viewDirection.z * semiCloseUpDistance
                    };

                    const closeUpPosition: Vector3 = {
                        x: targetLocation.x + viewDirection.x * closeUpDistance,
                        y: targetLocation.y + 2,
                        z: targetLocation.z + viewDirection.z * closeUpDistance
                    };

                    nearbyPlayers.forEach(player => {
                        player.playSound("mob.phantom.flap", { location: semiCloseUpPosition, volume: 1, pitch: 1.5 + (Math.random() * 0.3) });
                        player.camera.setCamera("minecraft:free", {
                            location: semiCloseUpPosition,
                            facingLocation: targetEntity.getHeadLocation(),
                            easeOptions: { easeTime: semiCloseUpTime, easeType: EasingType.InSine }
                        });

                        system.runTimeout(() => {
                            player.playSound("mob.phantom.flap", { location: closeUpPosition, volume: 1, pitch: 1.5 + (Math.random() * 0.3) });
                            player.camera.setCamera("minecraft:free", {
                                location: closeUpPosition,
                                facingLocation: {
                                    x: targetEntity.getHeadLocation().x,
                                    y: targetEntity.getHeadLocation().y - 1,
                                    z: targetEntity.getHeadLocation().z
                                },
                                easeOptions: { easeTime: finalCloseUpTime, easeType: EasingType.InSine }
                            });
                        }, semiCloseUpTime * 20);

                        system.runTimeout(() => {
                            player.camera.setCamera("minecraft:free", {
                                location: player.getHeadLocation(),
                                facingLocation: {
                                    x: targetEntity.getHeadLocation().x,
                                    y: targetEntity.getHeadLocation().y - 1,
                                    z: targetEntity.getHeadLocation().z
                                },
                                easeOptions: { easeTime: resetTime - finalCloseUpTime, easeType: EasingType.InOutSine }
                            });
                            displayEpicTitle(player, title, subtitle, titleDuration * 0.8);
                        }, finalCloseUpTime * 20);

                        system.runTimeout(() => {
                            player.setDynamicProperty('ptd_bb:boss_camera', false);
                            player.teleport(player.location, {
                                facingLocation: {
                                    x: targetEntity.getHeadLocation().x,
                                    y: targetEntity.getHeadLocation().y - 1,
                                    z: targetEntity.getHeadLocation().z
                                }
                            });
                            player.camera.clear();
                            if (targetEntity) targetEntity.setDynamicProperty('ptd_bb:boss_camera_ongoing', false);
                        }, resetTime * 20);
                    });
                }
            }, (keyframeTime / keyframeCount) * 20);
        }, keyframeTime / keyframeCount);
    } catch (error) {
        const nearbyPlayers: Player[] = targetEntity.dimension.getPlayers({
            location: targetLocation,
            maxDistance: 64
        });
        if (targetEntity) targetEntity.setDynamicProperty('ptd_bb:boss_camera_ongoing', false);
        player.camera.clear();
    }
}

export function displayEpicTitle(
    player: Player,
    title: string,
    subtitle: string,
    totalDuration: number = 0.8
): void {
    let currentTitle = "";
    let index = 0;

    // Calculate the interval dynamically based on the total duration and title length
    const intervalDuration: number = Math.ceil((totalDuration * 20) / title.length); // Total duration in ticks divided by title length

    const interval = system.runInterval(() => {
        if (index < title.length) {
            currentTitle += title[index];
            player.onScreenDisplay.setTitle(
                currentTitle + " ".repeat(title.length - index - 1),
                {
                    stayDuration: 100,
                    fadeInDuration: 0,
                    fadeOutDuration: 4
                }
            );
            player.playSound("random.pop", { volume: 1, pitch: 0.5 + (Math.random() * 0.01) });
            index++;
        } else {
            player.onScreenDisplay.setTitle(title, {
                stayDuration: 20,
                fadeInDuration: 1,
                fadeOutDuration: 1,
                subtitle: subtitle
            });
            player.playSound("random.totem", { volume: 0.1, pitch: 2 });
            system.clearRun(interval);
        }
    }, intervalDuration);
}