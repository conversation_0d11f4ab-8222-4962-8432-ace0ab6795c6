{
	"format_version": "1.21.0",
	"minecraft:entity": {
		"description": {
			"identifier": "ptd_dbb:grimhowl",
			"is_spawnable": true,
			"is_summonable": true,
			"is_experimental": false,
			"properties": {
			  "ptd_dbb:spawning_ticks": {
				"type": "int",
				"client_sync": true,
				"range": [
				  0,
				  114
				],
				"default": 0
			  },
			  "ptd_dbb:spawning": {
				"type": "bool",
				"client_sync": true,
				"default": true
			  },
			  "ptd_dbb:dead": {
				"type": "bool",
				"client_sync": true,
				"default": false
			  },
			  "ptd_dbb:death_timer": {
				"type": "int",
				"client_sync": true,
				"range": [
				  0,
				  100
				],
				"default": 0
			  },
			  "ptd_dbb:move_list": {
				"type": "enum",
				"client_sync": true,
				"default": "none",
				"values": [
				  "none",
				  "grimhowl_slash",
				  "grimhowl_spinning_slash",
				  "grimhowl_pounce",
				  "grimhowl_shadow_onslaught",
				  "grimhowl_claw_left",
				  "grimhowl_claw_right",
				  "grimhowl_backstep",
				  "grimhowl_roar",
				  "transition_to_swordless",
				  "transition_to_sword",
				  "arrow_shake"
				]
			  },
			  "ptd_dbb:enraged": {
				"type": "bool",
				"client_sync": true,
				"default": false
			  },
			  "ptd_dbb:sword_mode": {
				"type": "bool",
				"client_sync": true,
				"default": false
			  },
			  "ptd_dbb:arrow_hit_1": {
				"type": "bool",
				"client_sync": true,
				"default": false
			  },
			  "ptd_dbb:arrow_hit_2": {
				"type": "bool",
				"client_sync": true,
				"default": false
			  },
			  "ptd_dbb:arrow_hit_3": {
				"type": "bool",
				"client_sync": true,
				"default": false
			  },
			  "ptd_dbb:arrow_hit_4": {
				"type": "bool",
				"client_sync": true,
				"default": false
			  },
			  "ptd_dbb:arrow_hit_5": {
				"type": "bool",
				"client_sync": true,
				"default": false
			  },
			  "ptd_dbb:arrow_hit_6": {
				"type": "bool",
				"client_sync": true,
				"default": false
			  },
			  "ptd_dbb:arrow_hit_7": {
				"type": "bool",
				"client_sync": true,
				"default": false
			  },
			  "ptd_dbb:arrow_hit_8": {
				"type": "bool",
				"client_sync": true,
				"default": false
			  },
			  "ptd_dbb:arrow_hit_9": {
				"type": "bool",
				"client_sync": true,
				"default": false
			  },
			  "ptd_dbb:arrow_hit_10": {
				"type": "bool",
				"client_sync": true,
				"default": false
			  },
			  "ptd_dbb:arrow_hit_11": {
				"type": "bool",
				"client_sync": true,
				"default": false
			  },
			  "ptd_dbb:arrow_hit_12": {
				"type": "bool",
				"client_sync": true,
				"default": false
			  }
			}
		},
		"component_groups": {
			"ptd_dbb:movement": {
				"minecraft:navigation.walk": {
					"is_amphibious": false,
					"can_pass_doors": false,
					"can_break_doors": false,
					"avoid_portals": true
				},
				"minecraft:behavior.random_stroll": {
					"priority": 7,
					"speed_multiplier": 1
				},
				"minecraft:behavior.look_at_player": {
					"priority": 8,
					"look_distance": 6,
					"probability": 0.02
				},
				"minecraft:behavior.look_at_target": {
					"look_distance": 8,
					"probability": 0.02,
					"angle_of_view_vertical": 360,
					"angle_of_view_horizontal": 360
				},
				"minecraft:behavior.random_look_around": {
					"priority": 9
				}
			},
			"ptd_dbb:damage_sensor": {
				"minecraft:damage_sensor": {
					"triggers": [
						{
							"cause": "projectile",
							"damage_multiplier": 0.5,
							"deals_damage": true
						},
						{
							"cause": "fall",
							"deals_damage": false
						},
						{
						"on_damage": {
							"filters": {
								"any_of": [
									{
										"test": "has_damage",
										"value": "fatal"
									},
									{
										"test": "actor_health",
										"subject": "self",
										"value": 10
									}
								]
							},
							"event": "ptd_dbb:grimhowl_death"
						},
						"deals_damage": false
					}
					]
				},
				"minecraft:pushable": {
					"is_pushable": true,
					"is_pushable_by_piston": true
				}
			},
			"ptd_dbb:invulnerability": {
				"minecraft:damage_sensor": {
					"triggers": [
						{
							"cause": "all",
							"deals_damage": false
						}
					]
				},
				"minecraft:pushable": {
					"is_pushable": true,
					"is_pushable_by_piston": true
				}
			},
			"ptd_dbb:targeting": {
				"minecraft:behavior.hurt_by_target": {
        			"priority": 1,
        			"entity_types": {
        			  "filters": {
        			    "test": "is_family", "subject": "other", "operator": "!=", "value": "grimhowl"
        			  },
        			  "max_dist": 64
        			}
				},
        	"minecraft:behavior.nearest_prioritized_attackable_target": {
        	  "priority": 0,
        	  "must_see": true,
        	  "attack_interval": 1,
        	  "reselect_targets": true,
        	  "must_see_forget_duration": 0,
        	  "reevaluate_description": true,
        	  "entity_types": [
        	    {
        	      "priority": 0,
        	      "max_dist": 32,
        	      "filters": {
        	        "test": "is_family",
        	        "subject": "other",
        	        "value": "player"
        	      }
        	    },
        	    {
        	      "priority": 1,
        	      "max_dist": 32,
        	      "filters": {
        	        "all_of": [
        	          {
        	            "test": "is_family",
        	            "subject": "other",
        	            "value": "boss"
        	          },
        	          {
        	            "test": "is_family",
        	            "subject": "other",
        	            "operator": "!=",
        	            "value": "ptd_dbb_grimhowl"
        	          },
        	          {
        	            "test": "bool_property",
        	            "domain": "ptd_dbb:spawning",
        	            "subject": "other",
        	            "operator": "==",
        	            "value": false
        	          },
        	          {
        	            "test": "bool_property",
        	            "domain": "ptd_dbb:dead",
        	            "subject": "other",
        	            "operator": "==",
        	            "value": false
        	          }
        	        ]
        	      }
        	    },
        	    {
        	      "priority": 2,
        	      "max_dist": 32,
        	      "filters": {
        	        "all_of": [
        	          {
        	            "test": "is_family",
        	            "subject": "other",
        	            "value": "minion"
        	          },
        	          {
        	            "test": "is_family",
        	            "subject": "other",
        	            "operator": "!=",
        	            "value": "ptd_dbb_grimhowl"
        	          }
        	        ]
        	      }
        	    }
        	  ]
        	}
			},

			"ptd_dbb:grimhowl_sensor": {
				"minecraft:environment_sensor": {
					"triggers": [
						{
							"event": "ptd_dbb:speed_boost",
							"target": "self",
							"filters": {
								"test": "target_distance",
								"operator": ">=",
								"value": 10
							}
						},
						{
						  "event": "ptd_dbb:run_melee_range_attack",
						  "filters": {
							"all_of": [
							  {
								"test": "enum_property",
								"domain": "ptd_dbb:move_list",
								"subject": "self",
								"value": "none"
							  },
							  {
								  "test": "target_distance",
								  "subject": "self",
								  "operator": "<",
								  "value": 7
							  }
							]
						  }
						},
					  {
					    "event": "ptd_dbb:run_mid_range_attack",
					    "filters": {
					      "all_of": [
					        {
					          "test": "enum_property",
							  "domain": "ptd_dbb:move_list",
							  "subject": "self",
					          "value": "none"
					        },
							{
								"test": "target_distance",
								"subject": "self",
								"operator": ">=",
								"value": 10
							},
							{
								"test": "target_distance",
								"subject": "self",
								"operator": "<",
								"value": 18
							}
					      ]
					    }
					  },
					  {
					    "event": "ptd_dbb:run_long_range_attack",
					    "filters": {
					      "all_of": [
					        {
					          "test": "enum_property",
							  "domain": "ptd_dbb:move_list",
							  "subject": "self",
					          "value": "none"
					        },
							{
								"test": "target_distance",
								"subject": "self",
								"operator": ">=",
								"value": 18
							}
					      ]
					    }
					  }
					]
				}
			},

			"ptd_dbb:grimhowl_spawning": {
				"minecraft:is_collidable": {},
				"minecraft:timer": {
				  "time": 6.96,
				  "looping": false,
				  "time_down_event": {
					"event": "ptd_dbb:entity_spawned",
					"target": "self"
				  }
				}
			},

			//NORMAL
			"ptd_dbb:grimhowl_slash": {
				"minecraft:is_collidable": {},
				"minecraft:timer": {
				  "time": 3.25,
				  "looping": false,
				  "time_down_event": {
					"event": "ptd_dbb:attack_done",
					"target": "self"
				  }
				}
			},
			"ptd_dbb:grimhowl_spinning_slash": {
				"minecraft:is_collidable": {},
				"minecraft:timer": {
				  "time": 3.0,
				  "looping": false,
				  "time_down_event": {
					"event": "ptd_dbb:attack_done",
					"target": "self"
				  }
				}
			},
			"ptd_dbb:grimhowl_claw_left": {
				"minecraft:is_collidable": {},
				"minecraft:timer": {
				  "time": 1.5417,
				  "looping": false,
				  "time_down_event": {
					"event": "ptd_dbb:attack_done",
					"target": "self"
				  }
				}
			},
			"ptd_dbb:grimhowl_claw_right": {
				"minecraft:is_collidable": {},
				"minecraft:timer": {
				  "time": 1.5417,
				  "looping": false,
				  "time_down_event": {
					"event": "ptd_dbb:attack_done",
					"target": "self"
				  }
				}
			},
			"ptd_dbb:grimhowl_pounce": {
				"minecraft:is_collidable": {},
				"minecraft:timer": {
				  "time": 2.3333,
				  "looping": false,
				  "time_down_event": {
					"event": "ptd_dbb:attack_done",
					"target": "self"
				  }
				}
			},

			//ENRAGED
			"ptd_dbb:grimhowl_slash_enraged": {
				"minecraft:is_collidable": {},
				"minecraft:timer": {
				  "time": 1.5,
				  "looping": false,
				  "time_down_event": {
					"event": "ptd_dbb:attack_done",
					"target": "self"
				  }
				}
			},
			"ptd_dbb:grimhowl_spinning_slash_enraged": {
				"minecraft:is_collidable": {},
				"minecraft:timer": {
				  "time": 1.625,
				  "looping": false,
				  "time_down_event": {
					"event": "ptd_dbb:attack_done",
					"target": "self"
				  }
				}
			},
			"ptd_dbb:grimhowl_claw_left_enraged": {
				"minecraft:is_collidable": {},
				"minecraft:timer": {
				  "time": 0.7709,
				  "looping": false,
				  "time_down_event": {
					"event": "ptd_dbb:attack_done",
					"target": "self"
				  }
				}
			},
			"ptd_dbb:grimhowl_claw_right_enraged": {
				"minecraft:is_collidable": {},
				"minecraft:timer": {
				  "time": 0.7709,
				  "looping": false,
				  "time_down_event": {
					"event": "ptd_dbb:attack_done",
					"target": "self"
				  }
				}
			},
			"ptd_dbb:grimhowl_shadow_onslaught": {
				"minecraft:is_collidable": {},
				"minecraft:timer": {
				  "time": 2.8958,
				  "looping": false,
				  "time_down_event": {
					"event": "ptd_dbb:attack_done",
					"target": "self"
				  }
				}
			},

			//EXTRA
			"ptd_dbb:grimhowl_backstep": {
				"minecraft:timer": {
				  "time": 1.4167,
				  "looping": false,
				  "time_down_event": {
					"event": "ptd_dbb:attack_done",
					"target": "self"
				  }
				}
			},
			"ptd_dbb:grimhowl_roar": {
				"minecraft:timer": {
				  "time": 2.5,
				  "looping": false,
				  "time_down_event": {
					"event": "ptd_dbb:attack_done",
					"target": "self"
				  }
				}
			},
			"ptd_dbb:grimhowl_arrow_shake": {
				"minecraft:timer": {
				  "time": 2.0,
				  "looping": false,
				  "time_down_event": {
					"event": "ptd_dbb:grimhowl_arrow_shaked",
					"target": "self"
				  }
				}
			},
			"ptd_dbb:swordless_transition": {
				"minecraft:timer": {
				  "time": 2.7917,
				  "looping": false,
				  "time_down_event": {
					"event": "ptd_dbb:transitioned_to_swordless",
					"target": "self"
				  }
				}
			},
			"ptd_dbb:sword_transition": {
				"minecraft:timer": {
				  "time": 2.7917,
				  "looping": false,
				  "time_down_event": {
					"event": "ptd_dbb:transitioned_to_sword",
					"target": "self"
				  }
				}
			}
		},
		"components": {
			"minecraft:fire_immune": {},
			"minecraft:follow_range": {
			  "value": 64
			},
			"minecraft:knockback_resistance": {
				"value": 1,
				"max": 1
			},
			"minecraft:breathable": {
				"total_supply": 15,
				"suffocate_time": 0,
				"breathes_water": true
			},
			"minecraft:variable_max_auto_step": {
			  "base_value": 1.0625,
			  "jump_prevented_value": 1.0625
			},
			"minecraft:collision_box": {
				"width": 3.5,
				"height": 4.4
			},
			"minecraft:movement": {
				"value": 0.35
			},
			"minecraft:nameable": {},
			"minecraft:type_family": {
				"family": ["ptd_dbb_grimhowl", "monster", "boss"]
			},
			"minecraft:boss": {
				"hud_range": 55,
				"name": "Grimhowl",
				"should_darken_sky": true
			},
			"minecraft:health": {
				"value": 1200,
				"max": 1200
			},
			"minecraft:hurt_on_condition": {
				"damage_conditions": [
					{
						"filters": {
							"test": "in_lava",
							"subject": "self",
							"operator": "==",
							"value": true
						},
						"cause": "lava",
						"damage_per_tick": 4
					}
				]
			},
			"minecraft:attack": {
				"damage": 0
			},
			"minecraft:movement.basic": {
			},
			"minecraft:jump.static": {},
			"minecraft:behavior.melee_attack": {
				"priority": 3,
				"reach_multiplier": 0,
				"melee_fov": 15,
				"track_target": true
			},
			"minecraft:physics": {}
		},
		"events": {
			"minecraft:entity_spawned": {
				"add": {
					"component_groups": [
						"ptd_dbb:grimhowl_spawning",
						"ptd_dbb:invulnerability"
					]
				}
			},
			"ptd_dbb:entity_spawned": {
				"set_property": {
					"ptd_dbb:spawning": false
				},
				"add": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting",
						"ptd_dbb:grimhowl_sensor",
						"ptd_dbb:damage_sensor"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:grimhowl_spawning",
						"ptd_dbb:invulnerability"
					]
				}
			},

			// SCRIPT EVENTS
			"ptd_dbb:run_melee_range_attack": {
				"queue_command": {
					"command": [
						"scriptevent ptd_dbb:run_melee_range_attack"
					]
				}
			},

			"ptd_dbb:run_mid_range_attack": {
				"queue_command": {
					"command": [
						"scriptevent ptd_dbb:run_mid_range_attack"
					]
				}
			},

			"ptd_dbb:run_long_range_attack": {
				"queue_command": {
					"command": [
						"scriptevent ptd_dbb:run_long_range_attack"
					]
				}
			},

			"ptd_dbb:speed_boost": {
				"queue_command": {
					"command": [
						"effect @s speed 3 3 true"
					]
				}
			},

      // MOVE LIST

			"ptd_dbb:grimhowl_slash": {
				"set_property": {
					"ptd_dbb:move_list": "grimhowl_slash"
				},
				"add": {
					"component_groups": [
						"ptd_dbb:grimhowl_slash",
						"ptd_dbb:damage_sensor"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting"
					]
				}
			},
			"ptd_dbb:grimhowl_spinning_slash": {
				"set_property": {
					"ptd_dbb:move_list": "grimhowl_spinning_slash"
				},
				"add": {
					"component_groups": [
						"ptd_dbb:grimhowl_spinning_slash",
						"ptd_dbb:damage_sensor"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting"
					]
				}
			},
			"ptd_dbb:grimhowl_claw_left": {
				"set_property": {
					"ptd_dbb:move_list": "grimhowl_claw_left"
				},
				"add": {
					"component_groups": [
						"ptd_dbb:grimhowl_claw_left",
						"ptd_dbb:damage_sensor"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting"
					]
				}
			},
			"ptd_dbb:grimhowl_claw_right": {
				"set_property": {
					"ptd_dbb:move_list": "grimhowl_claw_right"
				},
				"add": {
					"component_groups": [
						"ptd_dbb:grimhowl_claw_right",
						"ptd_dbb:damage_sensor"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting"
					]
				}
			},
			"ptd_dbb:grimhowl_pounce": {
				"set_property": {
					"ptd_dbb:move_list": "grimhowl_pounce"
				},
				"add": {
					"component_groups": [
						"ptd_dbb:grimhowl_pounce",
						"ptd_dbb:damage_sensor"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting"
					]
				}
			},

			"ptd_dbb:grimhowl_slash_enraged": {
				"set_property": {
					"ptd_dbb:move_list": "grimhowl_slash"
				},
				"add": {
					"component_groups": [
						"ptd_dbb:grimhowl_slash_enraged",
						"ptd_dbb:damage_sensor"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting"
					]
				}
			},
			"ptd_dbb:grimhowl_spinning_slash_enraged": {
				"set_property": {
					"ptd_dbb:move_list": "grimhowl_spinning_slash"
				},
				"add": {
					"component_groups": [
						"ptd_dbb:grimhowl_spinning_slash_enraged",
						"ptd_dbb:damage_sensor"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting"
					]
				}
			},
			"ptd_dbb:grimhowl_claw_left_enraged": {
				"set_property": {
					"ptd_dbb:move_list": "grimhowl_claw_left"
				},
				"add": {
					"component_groups": [
						"ptd_dbb:grimhowl_claw_left_enraged",
						"ptd_dbb:damage_sensor"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting"
					]
				}
			},
			"ptd_dbb:grimhowl_claw_right_enraged": {
				"set_property": {
					"ptd_dbb:move_list": "grimhowl_claw_right"
				},
				"add": {
					"component_groups": [
						"ptd_dbb:grimhowl_claw_right_enraged",
						"ptd_dbb:damage_sensor"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting"
					]
				}
			},
			"ptd_dbb:grimhowl_shadow_onslaught": {
				"set_property": {
					"ptd_dbb:move_list": "grimhowl_shadow_onslaught"
				},
				"add": {
					"component_groups": [
						"ptd_dbb:grimhowl_shadow_onslaught",
						"ptd_dbb:damage_sensor"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting"
					]
				}
			},

			"ptd_dbb:grimhowl_backstep": {
				"set_property": {
					"ptd_dbb:move_list": "grimhowl_backstep"
				},
				"add": {
					"component_groups": [
						"ptd_dbb:grimhowl_backstep",
						"ptd_dbb:damage_sensor"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting"
					]
				}
			},
			"ptd_dbb:grimhowl_roar": {
				"set_property": {
					"ptd_dbb:move_list": "grimhowl_roar"
				},
				"add": {
					"component_groups": [
						"ptd_dbb:grimhowl_roar",
						"ptd_dbb:damage_sensor"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting"
					]
				}
			},

      // EXTRA
			"ptd_dbb:transition_to_swordless": {
				"set_property": {
					"ptd_dbb:move_list": "transition_to_swordless"
				},
				"add": {
					"component_groups": [
						"ptd_dbb:swordless_transition",
						"ptd_dbb:invulnerability"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting",
						"ptd_dbb:damage_sensor"
					]
				}
			},
			"ptd_dbb:transition_to_sword": {
				"set_property": {
					"ptd_dbb:move_list": "transition_to_sword"
				},
				"add": {
					"component_groups": [
						"ptd_dbb:sword_transition",
						"ptd_dbb:invulnerability"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting",
						"ptd_dbb:damage_sensor"
					]
				}
			},
			"ptd_dbb:grimhowl_arrow_shake": {
				"set_property": {
					"ptd_dbb:move_list": "arrow_shake"
				},
				"add": {
					"component_groups": [
						"ptd_dbb:grimhowl_arrow_shake",
						"ptd_dbb:invulnerability"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting",
						"ptd_dbb:damage_sensor"
					]
				}
			},

			"ptd_dbb:attack_done": {
				"set_property": {
					"ptd_dbb:move_list": "none"
				},
				"add": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:grimhowl_slash",
						"ptd_dbb:grimhowl_spinning_slash",
						"ptd_dbb:grimhowl_claw_left",
						"ptd_dbb:grimhowl_claw_right",
						"ptd_dbb:grimhowl_pounce",
						"ptd_dbb:grimhowl_backstep",
						"ptd_dbb:grimhowl_slash_enraged",
						"ptd_dbb:grimhowl_spinning_slash_enraged",
						"ptd_dbb:grimhowl_claw_left_enraged",
						"ptd_dbb:grimhowl_claw_right_enraged",
						"ptd_dbb:grimhowl_shadow_onslaught",
						"ptd_dbb:grimhowl_roar",
						"ptd_dbb:grimhowl_arrow_shake"
					]
				}
			},

			"ptd_dbb:grimhowl_death": {
				"set_property": {
					"ptd_dbb:move_list": "none",
					"ptd_dbb:dead": true
				},
				"queue_command": {
					"command": [
						"scriptevent ptd_dbb:grimhowl_death"
					]
				},
				"add": {
					"component_groups": [
						"ptd_dbb:invulnerability"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:grimhowl_slash",
						"ptd_dbb:grimhowl_spinning_slash",
						"ptd_dbb:grimhowl_claw_left",
						"ptd_dbb:grimhowl_claw_right",
						"ptd_dbb:grimhowl_pounce",
						"ptd_dbb:grimhowl_backstep",
						"ptd_dbb:grimhowl_slash_enraged",
						"ptd_dbb:grimhowl_spinning_slash_enraged",
						"ptd_dbb:grimhowl_claw_left_enraged",
						"ptd_dbb:grimhowl_claw_right_enraged",
						"ptd_dbb:grimhowl_shadow_onslaught",
						"ptd_dbb:grimhowl_roar",
						"ptd_dbb:movement",
						"ptd_dbb:targeting",
						"ptd_dbb:damage_sensor",
						"ptd_dbb:grimhowl_sensor"
					]
				}
			},

			"ptd_dbb:grimhowl_arrow_shaked": {
				"set_property": {
					"ptd_dbb:move_list": "none"
				},
				"queue_command": {
					"command": [
						"scriptevent ptd_dbb:grimhowl_arrows_reset",
						"scriptevent ptd_dbb:grimhowl_do_the_roar"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:invulnerability"
					]
				},
				"add": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting",
						"ptd_dbb:damage_sensor"
					]
				}
			},
			"ptd_dbb:transitioned_to_swordless": {
				"set_property": {
					"ptd_dbb:sword_mode": true
				},
				"queue_command": {
					"command": [
						"scriptevent ptd_dbb:grimhowl_do_the_roar"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:invulnerability"
					]
				},
				"add": {
					"component_groups": [
						"ptd_dbb:movement",
						"ptd_dbb:targeting",
						"ptd_dbb:damage_sensor"
					]
				}
			},
			"ptd_dbb:transitioned_to_sword": {
				"set_property": {
					"ptd_dbb:sword_mode": true
				},
				"queue_command": {
					"command": [
						"scriptevent ptd_dbb:grimhowl_do_the_roar"
					]
				},
				"remove": {
					"component_groups": [
						"ptd_dbb:invulnerability"
					]
				}
			},

			"ptd_dbb:arrow_hit_1": {
				"set_property": {
					"ptd_dbb:arrow_hit_1": true
				}
			},
			"ptd_dbb:arrow_hit_2": {
				"set_property": {
					"ptd_dbb:arrow_hit_2": true
				}
			},
			"ptd_dbb:arrow_hit_3": {
				"set_property": {
					"ptd_dbb:arrow_hit_3": true
				}
			},
			"ptd_dbb:arrow_hit_4": {
				"set_property": {
					"ptd_dbb:arrow_hit_4": true
				}
			},
			"ptd_dbb:arrow_hit_5": {
				"set_property": {
					"ptd_dbb:arrow_hit_5": true
				}
			},
			"ptd_dbb:arrow_hit_6": {
				"set_property": {
					"ptd_dbb:arrow_hit_6": true
				}
			},
			"ptd_dbb:arrow_hit_7": {
				"set_property": {
					"ptd_dbb:arrow_hit_7": true
				}
			},
			"ptd_dbb:arrow_hit_8": {
				"set_property": {
					"ptd_dbb:arrow_hit_8": true
				}
			},
			"ptd_dbb:arrow_hit_9": {
				"set_property": {
					"ptd_dbb:arrow_hit_9": true
				}
			},
			"ptd_dbb:arrow_hit_10": {
				"set_property": {
					"ptd_dbb:arrow_hit_10": true
				}
			},
			"ptd_dbb:arrow_hit_11": {
				"set_property": {
					"ptd_dbb:arrow_hit_11": true
				}
			},
			"ptd_dbb:arrow_hit_12": {
				"set_property": {
					"ptd_dbb:arrow_hit_12": true
				}
			}
		}
	}
}