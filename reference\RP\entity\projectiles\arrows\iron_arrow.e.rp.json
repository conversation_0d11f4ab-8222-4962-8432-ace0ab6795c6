{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "va:iron_arrow", "materials": {"default": "arrow"}, "textures": {"default": "textures/entity/projectiles/arrows/iron_arrow"}, "geometry": {"default": "geometry.arrow"}, "animations": {"move": "animation.custom_arrow.move"}, "scripts": {"pre_animation": ["variable.shake = query.shake_time - query.frame_alpha;", "variable.shake_power = variable.shake > 0.0 ? -Math.sin(variable.shake * 200.0) * variable.shake : 0.0;"], "animate": ["move"]}, "render_controllers": ["controller.render.arrow"]}}}